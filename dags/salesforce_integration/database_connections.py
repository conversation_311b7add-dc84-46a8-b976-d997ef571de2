"""
ETL Consolidado - Conexões Simplificadas para Airflow
Conexões diretas de banco de dados otimizadas para execução de tasks individuais.
"""

import time
import logging
from typing import Optional, Dict, Any, List, Union
from contextlib import contextmanager

try:
    import pymssql
    PYMSSQL_AVAILABLE = True
except ImportError:
    PYMSSQL_AVAILABLE = False

try:
    import psycopg2
    PSYCOPG2_AVAILABLE = True
except ImportError:
    PSYCOPG2_AVAILABLE = False

try:
    import mysql.connector
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False

from salesforce_integration.config import (
    DATABASE_CONFIGS,
    RETRY_CONFIGS,
    TIMEOUT_CONFIGS,
    LOGGING_CONFIG
)

# =============================================================================
# CONFIGURAÇÃO DE LOGGING
# =============================================================================

logger = logging.getLogger(__name__)

# =============================================================================
# FUNÇÕES DE CONEXÃO SIMPLIFICADAS
# =============================================================================

def create_connection(db_name: str) -> Any:
    """Cria conexão direta ao banco com retry automático"""
    config = DATABASE_CONFIGS.get(db_name)
    if not config:
        raise ValueError(f"Configuração de banco '{db_name}' não encontrada")

    return _create_connection_with_retry(db_name, config)

def _create_connection_with_retry(db_name: str, config: Dict[str, Any]) -> Any:
    """Cria conexão com retry automático e failover"""
    max_attempts = config.get('retry_attempts', RETRY_CONFIGS['max_attempts'])
    base_delay = config.get('retry_delay', RETRY_CONFIGS['base_delay'])
    
    for attempt in range(max_attempts):
        try:
            # Tenta conexão primária
            connection = _create_single_connection(db_name, config, 'primary')
            if connection:
                logger.debug(f"✅ Conexão estabelecida com '{db_name}' (tentativa {attempt + 1})")
                return connection
            
            # Se NewCon, tenta failover para secundário
            if db_name == 'newcon' and 'secondary' in config:
                logger.warning(f"Tentando failover para servidor secundário (tentativa {attempt + 1})")
                connection = _create_single_connection(db_name, config, 'secondary')
                if connection:
                    logger.info(f"✅ Conexão estabelecida com '{db_name}' via failover")
                    return connection
            
        except Exception as e:
            logger.warning(f"Tentativa {attempt + 1} falhou para '{db_name}': {e}")
            
            if attempt < max_attempts - 1:
                delay = base_delay * (2 ** attempt)  # Exponential backoff
                logger.info(f"Aguardando {delay}s antes da próxima tentativa...")
                time.sleep(delay)
            else:
                logger.error(f"❌ Todas as tentativas falharam para '{db_name}'")
                raise
    
    raise Exception(f"Não foi possível conectar ao banco '{db_name}' após {max_attempts} tentativas")

def _create_single_connection(db_name: str, config: Dict[str, Any], server_type: str = 'primary') -> Any:
    """Cria uma única conexão direta"""
    db_type = config.get('type')
    server_config = config.get(server_type, config)
    
    if db_type == 'mssql':
        if not PYMSSQL_AVAILABLE:
            raise ImportError("pymssql não está instalado")
        
        return pymssql.connect(
            server=server_config['host'],
            port=server_config['port'],
            database=server_config['database'],
            user=server_config['username'],
            password=server_config['password'],
            timeout=server_config.get('timeout', 30)
        )
    
    elif db_type == 'postgresql':
        if not PSYCOPG2_AVAILABLE:
            raise ImportError("psycopg2 não está instalado")
        
        return psycopg2.connect(
            host=server_config['host'],
            port=server_config['port'],
            database=server_config['database'],
            user=server_config['username'],
            password=server_config['password']
        )
    
    elif db_type == 'mysql':
        if not MYSQL_AVAILABLE:
            raise ImportError("mysql-connector-python não está instalado")
        
        return mysql.connector.connect(
            host=server_config['host'],
            port=server_config['port'],
            database=server_config['database'],
            user=server_config['username'],
            password=server_config['password']
        )
    
    else:
        raise ValueError(f"Tipo de banco não suportado: {db_type}")

# =============================================================================
# CONTEXT MANAGERS (Interface Compatível)
# =============================================================================

@contextmanager
def get_database_connection(db_name: str):
    """Context manager para obter conexão de banco"""
    connection = None
    try:
        connection = create_connection(db_name)
        yield connection
    finally:
        if connection:
            try:
                connection.close()
                logger.debug(f"Conexão fechada para '{db_name}'")
            except Exception as e:
                logger.warning(f"Erro ao fechar conexão '{db_name}': {e}")

@contextmanager
def get_database_cursor(db_name: str):
    """Context manager para obter cursor de banco"""
    connection = None
    cursor = None
    try:
        connection = create_connection(db_name)
        cursor = connection.cursor()
        yield cursor
    finally:
        if cursor:
            try:
                cursor.close()
            except Exception as e:
                logger.warning(f"Erro ao fechar cursor '{db_name}': {e}")
        if connection:
            try:
                connection.close()
                logger.debug(f"Conexão fechada para '{db_name}'")
            except Exception as e:
                logger.warning(f"Erro ao fechar conexão '{db_name}': {e}")

# =============================================================================
# FUNÇÕES DE CONVENIÊNCIA (Interface Compatível)
# =============================================================================

def get_newcon_connection():
    """Obtém conexão NewCon com failover automático"""
    return create_connection('newcon')

def get_dw_corporativo_connection():
    """Obtém conexão DW Corporativo"""
    return create_connection('dw_corporativo')

def get_orbbits_connection():
    """Obtém conexão Orbbits"""
    return create_connection('orbbits')

def test_connection(db_name: str) -> bool:
    """Testa conexão específica"""
    try:
        with get_database_cursor(db_name) as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            return result is not None
    except Exception as e:
        logger.error(f"❌ Teste de conexão falhou para '{db_name}': {e}")
        return False

def test_all_connections() -> Dict[str, bool]:
    """Testa todas as conexões"""
    results = {}
    logger.info("Testando todas as conexões...")
    
    for db_name in DATABASE_CONFIGS.keys():
        logger.info(f"Testando conexão '{db_name}'...")
        results[db_name] = test_connection(db_name)
        
        if results[db_name]:
            logger.info(f"✅ Conexão '{db_name}' OK")
        else:
            logger.error(f"❌ Conexão '{db_name}' FALHOU")
    
    success_count = sum(1 for success in results.values() if success)
    total_count = len(results)
    
    logger.info(f"Resultado: {success_count}/{total_count} conexões bem-sucedidas")
    
    return results

# =============================================================================
# FUNÇÕES DE QUERY AUXILIARES (Interface Compatível)
# =============================================================================

def execute_query(db_name: str, query: str, params: Optional[tuple] = None) -> List[tuple]:
    """Executa query e retorna resultados"""
    with get_database_cursor(db_name) as cursor:
        cursor.execute(query, params or ())
        return cursor.fetchall()

def execute_query_single(db_name: str, query: str, params: Optional[tuple] = None) -> Optional[tuple]:
    """Executa query e retorna resultado único"""
    with get_database_cursor(db_name) as cursor:
        cursor.execute(query, params or ())
        return cursor.fetchone()

def execute_query_dict(db_name: str, query: str, params: Optional[tuple] = None) -> List[Dict[str, Any]]:
    """Executa query e retorna resultados como dicionário"""
    with get_database_cursor(db_name) as cursor:
        cursor.execute(query, params or ())
        columns = [desc[0] for desc in cursor.description]
        return [dict(zip(columns, row)) for row in cursor.fetchall()]

# =============================================================================
# VALIDAÇÃO E DIAGNÓSTICO (Interface Compatível)
# =============================================================================

def validate_database_config(db_name: str) -> tuple:
    """Valida configuração de banco específico"""
    config = DATABASE_CONFIGS.get(db_name)
    
    if not config:
        return False, f"Configuração '{db_name}' não encontrada"
    
    required_fields = ['type', 'host', 'database', 'username', 'password']
    missing_fields = []
    
    # Verifica campos obrigatórios (considerando estrutura primary/secondary)
    primary_config = config.get('primary', config)
    
    # Para NewCon, verifica se tem estrutura primary/secondary correta
    if 'primary' in config and 'secondary' in config:
        for field in required_fields:
            if field not in primary_config or not primary_config[field]:
                missing_fields.append(field)
    else:
        # Para outros bancos, verifica diretamente
        for field in required_fields:
            if field not in config or not config[field]:
                missing_fields.append(field)
    
    if missing_fields:
        return False, f"Campos obrigatórios ausentes: {', '.join(missing_fields)}"
    
    return True, "Configuração válida"

def diagnose_connection_issues(db_name: str) -> Dict[str, Any]:
    """Diagnostica problemas de conexão"""
    diagnosis = {
        'database': db_name,
        'config_valid': False,
        'connection_test': False,
        'issues': [],
        'recommendations': []
    }
    
    # Valida configuração
    is_valid, message = validate_database_config(db_name)
    diagnosis['config_valid'] = is_valid
    
    if not is_valid:
        diagnosis['issues'].append(f"Configuração inválida: {message}")
        diagnosis['recommendations'].append("Verificar variáveis de ambiente")
    
    # Testa conexão
    if is_valid:
        diagnosis['connection_test'] = test_connection(db_name)
        
        if not diagnosis['connection_test']:
            diagnosis['issues'].append("Falha na conexão com o banco")
            diagnosis['recommendations'].append("Verificar rede e credenciais")
    
    return diagnosis

# =============================================================================
# FUNÇÕES DE COMPATIBILIDADE (Deprecated mas Mantidas)
# =============================================================================

def initialize_all_connections():
    """Inicializa todas as conexões (deprecated - mantido para compatibilidade)"""
    logger.info("🔄 initialize_all_connections() deprecated - conexões são criadas sob demanda")
    return True

def close_all_connections():
    """Fecha todas as conexões (deprecated - mantido para compatibilidade)"""
    logger.info("🔄 close_all_connections() deprecated - conexões são fechadas automaticamente")
    return True

def get_connection_stats() -> Dict[str, Any]:
    """Retorna estatísticas das conexões (deprecated - sem pooling)"""
    logger.info("🔄 get_connection_stats() deprecated - sem pooling não há estatísticas")
    return {"message": "Connection pooling removed - no stats available"}

def startup():
    """Função de inicialização (deprecated - mantido para compatibilidade)"""
    logger.info("🔄 Sistema simplificado - inicialização não necessária")
    results = test_all_connections()
    failed_connections = [db for db, success in results.items() if not success]
    
    if failed_connections:
        logger.warning(f"Conexões com falha: {', '.join(failed_connections)}")
    else:
        logger.info("✅ Todas as conexões estão funcionando")

def shutdown():
    """Função de finalização (deprecated - mantido para compatibilidade)"""
    logger.info("🔄 Sistema simplificado - finalização não necessária")

# =============================================================================
# MAIN PARA TESTES
# =============================================================================

if __name__ == "__main__":
    import sys
    
    logging.basicConfig(level=logging.INFO)
    
    try:
        startup()
        
        # Testa cada conexão individualmente
        for db_name in DATABASE_CONFIGS.keys():
            print(f"\n{'='*50}")
            print(f"Testando {db_name.upper()}")
            print(f"{'='*50}")
            
            diagnosis = diagnose_connection_issues(db_name)
            
            print(f"Config válida: {diagnosis['config_valid']}")
            print(f"Conexão OK: {diagnosis['connection_test']}")
            
            if diagnosis['issues']:
                print(f"Problemas: {', '.join(diagnosis['issues'])}")
            
            if diagnosis['recommendations']:
                print(f"Recomendações: {', '.join(diagnosis['recommendations'])}")
        
    except Exception as e:
        logger.error(f"❌ Erro durante teste: {e}")
        sys.exit(1)
    finally:
        shutdown()