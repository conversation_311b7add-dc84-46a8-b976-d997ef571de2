#!/usr/bin/env python3
"""
Script de Teste - Arquitetura por Unidade de Negócio

Testa a nova arquitetura de DAGs separadas por unidade de negócio.
Valida configurações, importações e estrutura das DAGs.

Uso:
    python test_business_units.py
    python test_business_units.py --unit consorcio
    python test_business_units.py --unit seguros
    python test_business_units.py --validate-config
"""

import sys
import os
import argparse
import logging
from datetime import datetime
from typing import Dict, Any, List

# Adicionar o diretório atual e o diretório pai ao path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, current_dir)
sys.path.insert(0, parent_dir)

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_imports():
    """Testa se todas as importações estão funcionando"""
    logger.info("🔍 Testando importações...")
    
    try:
        # Testa importações da nova arquitetura
        from dag_factory.business_unit_config import (
            get_business_unit_data_sources,
            get_business_unit_data_extensions,
            get_business_unit_dag_config,
            get_available_business_units,
            validate_business_unit
        )
        logger.info("✅ business_unit_config importado com sucesso")

        from dag_factory.base_dag import BaseSalesforceDAG
        logger.info("✅ BaseSalesforceDAG importado com sucesso")

        from dag_factory.dag_builder import (
            ConsorcioSalesforceDAG,
            SegurosSalesforceDAG,
            create_consorcio_dag,
            create_seguros_dag
        )
        logger.info("✅ dag_builder importado com sucesso")

        return True
        
    except ImportError as e:
        logger.error(f"❌ Erro de importação: {e}")
        return False

def test_business_unit_config():
    """Testa configurações das unidades de negócio"""
    logger.info("🔍 Testando configurações das unidades de negócio...")
    
    try:
        from dag_factory.business_unit_config import (
            get_available_business_units,
            validate_business_unit,
            get_business_unit_data_sources,
            get_business_unit_data_extensions,
            get_business_unit_dag_config
        )
        
        # Testa unidades disponíveis
        units = get_available_business_units()
        logger.info(f"📋 Unidades disponíveis: {units}")
        
        expected_units = ['consorcio', 'seguros']
        for unit in expected_units:
            if unit not in units:
                logger.error(f"❌ Unidade {unit} não encontrada")
                return False
            
            # Valida unidade
            if not validate_business_unit(unit):
                logger.error(f"❌ Validação falhou para {unit}")
                return False
            
            # Testa configurações
            data_sources = get_business_unit_data_sources(unit)
            data_extensions = get_business_unit_data_extensions(unit)
            dag_config = get_business_unit_dag_config(unit)
            
            logger.info(f"✅ {unit.title()}:")
            logger.info(f"   - Fontes: {len(data_sources.get('extraction_tasks', []))} tarefas")
            logger.info(f"   - Extensions: {len(data_extensions)} tabelas")
            logger.info(f"   - DAG ID: {dag_config.get('dag_id', 'N/A')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro ao testar configurações: {e}")
        return False

def test_dag_creation(business_unit: str = None):
    """Testa criação das DAGs"""
    logger.info(f"🔍 Testando criação de DAGs{f' para {business_unit}' if business_unit else ''}...")
    
    try:
        from dag_factory.dag_builder import (
            create_consorcio_dag,
            create_seguros_dag
        )
        
        units_to_test = [business_unit] if business_unit else ['consorcio', 'seguros']
        
        for unit in units_to_test:
            logger.info(f"📋 Testando DAG {unit}...")
            
            if unit == 'consorcio':
                dag = create_consorcio_dag('csv')  # Usa modo CSV para teste
            elif unit == 'seguros':
                dag = create_seguros_dag('csv')  # Usa modo CSV para teste
            else:
                logger.error(f"❌ Unidade desconhecida: {unit}")
                continue
            
            # Valida DAG criada
            if dag is None:
                logger.error(f"❌ DAG {unit} não foi criada")
                return False
            
            logger.info(f"✅ DAG {unit} criada com sucesso:")
            logger.info(f"   - ID: {dag.dag_id}")
            logger.info(f"   - Descrição: {dag.description}")
            logger.info(f"   - Schedule: {dag.schedule_interval}")
            logger.info(f"   - Tags: {dag.tags}")
            logger.info(f"   - Tarefas: {len(dag.task_dict)} tarefas")
            
            # Lista tarefas
            task_ids = list(dag.task_dict.keys())
            logger.info(f"   - Task IDs: {task_ids}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro ao criar DAGs: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_dag_files():
    """Testa se os arquivos de DAG podem ser importados"""
    logger.info("🔍 Testando arquivos de DAG...")
    
    try:
        # Testa importação dos arquivos de DAG
        import dag_consorcio as dag_consorcio_module
        import dag_seguros as dag_seguros_module
        
        # Verifica se as DAGs foram criadas
        consorcio_dag = getattr(dag_consorcio_module, 'dag', None)
        seguros_dag = getattr(dag_seguros_module, 'dag', None)
        
        if consorcio_dag is None:
            logger.error("❌ DAG Consórcio não encontrada no módulo")
            return False
        
        if seguros_dag is None:
            logger.error("❌ DAG Seguros não encontrada no módulo")
            return False
        
        logger.info("✅ Arquivos de DAG importados com sucesso:")
        logger.info(f"   - Consórcio: {consorcio_dag.dag_id}")
        logger.info(f"   - Seguros: {seguros_dag.dag_id}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro ao importar arquivos de DAG: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_directory_structure():
    """Testa se a estrutura de diretórios está correta"""
    logger.info("🔍 Testando estrutura de diretórios...")
    
    base_dir = os.path.dirname(os.path.abspath(__file__))
    
    required_files = [
        'dag_factory/__init__.py',
        'dag_factory/business_unit_config.py',
        'dag_factory/base_dag.py',
        'dag_factory/dag_builder.py',
        'dag_consorcio.py',
        'dag_seguros.py',
        'README_BUSINESS_UNITS.md'
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = os.path.join(base_dir, file_path)
        if not os.path.exists(full_path):
            missing_files.append(file_path)
    
    if missing_files:
        logger.error(f"❌ Arquivos faltando: {missing_files}")
        return False
    
    logger.info("✅ Estrutura de diretórios está correta")
    return True

def run_all_tests():
    """Executa todos os testes"""
    logger.info("🚀 Iniciando testes da arquitetura por unidade de negócio...")
    
    tests = [
        ("Estrutura de Diretórios", test_directory_structure),
        ("Importações", test_imports),
        ("Configurações", test_business_unit_config),
        ("Criação de DAGs", lambda: test_dag_creation()),
        ("Arquivos de DAG", test_dag_files),
    ]
    
    results = {}
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"🧪 Executando: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name}: PASSOU")
            else:
                logger.error(f"❌ {test_name}: FALHOU")
                
        except Exception as e:
            logger.error(f"❌ {test_name}: ERRO - {e}")
            results[test_name] = False
    
    # Relatório final
    logger.info(f"\n{'='*50}")
    logger.info("📊 RELATÓRIO FINAL")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSOU" if result else "❌ FALHOU"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nResultado: {passed}/{total} testes passaram")
    
    if passed == total:
        logger.info("🎉 Todos os testes passaram! Arquitetura está funcionando.")
        return True
    else:
        logger.error("💥 Alguns testes falharam. Verifique os logs acima.")
        return False

def main():
    """Função principal"""
    parser = argparse.ArgumentParser(description='Testa arquitetura por unidade de negócio')
    parser.add_argument('--unit', choices=['consorcio', 'seguros'], 
                       help='Testa apenas uma unidade específica')
    parser.add_argument('--validate-config', action='store_true',
                       help='Valida apenas configurações')
    
    args = parser.parse_args()
    
    if args.validate_config:
        success = test_business_unit_config()
    elif args.unit:
        success = test_dag_creation(args.unit)
    else:
        success = run_all_tests()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
