
-- =====================================================
-- CONSULTA: NewCon Leads
-- DESCRIÇÃO: Extrai dados de leads do sistema NewCon
-- FONTE: Banco NewCon (SQL Server)
-- =====================================================

WITH propostas_nao_alocadas AS (
    SELECT
        'Leads' AS end_point,
        concc030.id_documento,
        corcc023.cd_inscricao_nacional AS CNPJCPF,
        corcc030.e_mail AS Email,
        CAST(concc030.dt_cadastro AS DATE) AS Dt_cadastro,
        CONCAT('(55', ISNULL(ddd_telefone.ddd, ddd_endereco.ddd), corcc027.telefone, ')') AS Celular,
        'br' AS Locale,
        LEFT(corcc023.nm_pessoa, CHARINDEX(' ', corcc023.nm_pessoa + ' ') - 1) AS PrimeiroNome,
        corcc023.nm_pessoa AS NomeCompleto,
        ddd_endereco.nm_cidade AS Cidade,
        ddd_endereco.id_uf AS Estado,
        'Bamaq Consórcio' AS TipoEmpresa,
        conbe003.nm_produto AS TipoBem,
        'Consórcio' AS TipoSimulacao,
        CAST(concc030.vl_bem AS NUMERIC(18,2)) AS ValorSimulacao,

        -- Campos de marketing (não utilizados no NewCon)
        NULL AS Campaign,
        NULL AS Source,
        NULL AS Medium,
        NULL AS Term,
        NULL AS Content,

        conve001.nm_fantasia AS PontoVenda,
        conve041.nm_plano_venda AS PlanoVenda,
        CAST(corcc023a.dt_nascimento AS DATE) AS dt_nascimento,

        -- Campos de Opt-in (não utilizados no NewCon)
        NULL AS Optin_seguros_email,
        NULL AS Optin_seguros_SMS,
        NULL AS Optin_seguros_wpp,
        NULL AS Optin_capital_email,
        NULL AS Optin_capital_SMS,
        NULL AS Optin_capital_whatsapp,

        CAST(concc030.dh_inclusao AS DATE) AS Dt_simulacao,
        conve014.nm_fantasia AS NomeVendedor
    FROM concc030

        -- Dados da pessoa
        INNER JOIN corcc023 ON corcc023.id_pessoa = concc030.id_pessoa
        LEFT JOIN corcc023a ON corcc023a.id_pessoa = corcc023.id_pessoa

        -- Verificação de cota (para filtrar leads)
        LEFT JOIN concc036 ON concc036.id_empresa = concc030.id_empresa
            AND concc036.id_tipo_documento = concc030.id_tipo_documento
            AND concc036.id_documento = concc030.id_documento

        -- Dados de contato
        LEFT JOIN corcc030 ON corcc030.id_pessoa = concc030.id_pessoa
            AND corcc030.id_e_mail = concc030.id_e_mail

        -- Dados de endereço
        INNER JOIN corcc026 ON corcc026.id_pessoa = concc030.id_pessoa
            AND corcc026.id_endereco = concc030.id_endereco

        -- Dados de telefone
        INNER JOIN corcc027 ON corcc027.id_pessoa = concc030.id_pessoa
            AND corcc027.id_telefone = concc030.id_telefone

        -- DDDs para telefone e endereço
        INNER JOIN corcc015 ddd_endereco ON ddd_endereco.id_cidade = corcc026.id_cidade
        INNER JOIN corcc015 ddd_telefone ON ddd_telefone.id_cidade = corcc027.id_cidade

        -- Dados do bem/produto
        INNER JOIN conbe007 ON conbe007.id_bem = concc030.id_bem
        LEFT JOIN conbe023 ON conbe007.ID_CONBE023 = conbe023.ID_CONBE023
        LEFT JOIN conbe003 ON conbe023.ID_Produto = conbe003.ID_Produto

        -- Dados do ponto de venda
        INNER JOIN conve001 ON conve001.id_ponto_venda = concc030.id_ponto_venda

        -- Dados do plano de venda
        INNER JOIN conve041 ON conve041.id_plano_venda = concc030.id_plano_venda

        -- Dados do vendedor
        INNER JOIN conve014 ON conve014.id_comissionado = concc030.id_comissionado

    WHERE concc036.id_cota IS NULL
        AND DATEDIFF(dd, CAST(concc030.dt_cadastro AS DATE), CAST(GETDATE() AS DATE)) <= 30
),

vendas AS (
    SELECT
        'Leads' AS end_point,
        concc036.id_documento,
        corcc023.cd_inscricao_nacional AS CNPJCPF,
        corcc030.e_mail AS Email,
        conve002d.dt_cadastro AS Dt_cadastro,
        CONCAT('(55', ISNULL(ddd_telefone.ddd, ddd_endereco.ddd), corcc027.telefone, ')') AS Celular,
        'br' AS Locale,
        LEFT(corcc023.nm_pessoa, CHARINDEX(' ', corcc023.nm_pessoa + ' ') - 1) AS PrimeiroNome,
        corcc023.nm_pessoa AS NomeCompleto,
        ddd_endereco.nm_cidade AS Cidade,
        ddd_endereco.id_uf AS Estado,
        'Bamaq Consórcio' AS TipoEmpresa,
        conbe003.nm_produto AS TipoBem,
        'Consórcio' AS TipoSimulacao,
        conve002d.vl_credito AS ValorSimulacao,

        -- Campos de marketing (não utilizados no NewCon)
        NULL AS Campaign,
        NULL AS Source,
        NULL AS Medium,
        NULL AS Term,
        NULL AS Content,

        conve001.nm_fantasia AS PontoVenda,
        conve041.nm_plano_venda AS PlanoVenda,
        CAST(corcc023a.dt_nascimento AS DATE) AS dt_nascimento,

        -- Campos de Opt-in (não utilizados no NewCon)
        NULL AS Optin_seguros_email,
        NULL AS Optin_seguros_SMS,
        NULL AS Optin_seguros_wpp,
        NULL AS Optin_capital_email,
        NULL AS Optin_capital_SMS,
        NULL AS Optin_capital_whatsapp,

        CAST(concc030.dh_inclusao AS DATE) AS Dt_simulacao,
        conve014.nm_fantasia AS NomeVendedor
    FROM conve002

        -- Dados da venda
        INNER JOIN conve002d ON conve002d.id_cota = conve002.id_cota

        -- Dados do documento/cota
        LEFT JOIN concc036 ON concc036.id_cota = conve002.id_cota

        -- Dados da proposta original
        LEFT JOIN concc030 ON concc030.id_empresa = concc036.id_empresa
            AND concc030.id_tipo_documento = concc036.id_tipo_documento
            AND concc030.id_documento = concc036.id_documento

        -- Dados da pessoa
        INNER JOIN corcc023 ON corcc023.id_pessoa = conve002.id_pessoa
        LEFT JOIN corcc023a ON corcc023a.id_pessoa = corcc023.id_pessoa

        -- Dados de contato
        INNER JOIN corcc030 ON corcc030.id_pessoa = conve002.id_pessoa
            AND corcc030.id_e_mail = conve002.id_e_mail

        -- Dados de endereço
        INNER JOIN corcc026 ON corcc026.id_pessoa = conve002.id_pessoa
            AND corcc026.id_endereco = conve002.id_endereco

        -- Dados de telefone
        INNER JOIN corcc027 ON corcc027.id_pessoa = conve002.id_pessoa
            AND corcc027.id_telefone = conve002.id_telefone

        -- DDDs para telefone e endereço
        INNER JOIN corcc015 ddd_endereco ON ddd_endereco.id_cidade = corcc026.id_cidade
        INNER JOIN corcc015 ddd_telefone ON ddd_telefone.id_cidade = corcc027.id_cidade

        -- Dados do bem/produto
        INNER JOIN conbe007 ON conbe007.id_bem = conve002d.id_bem
        LEFT JOIN conbe023 ON conbe007.ID_CONBE023 = conbe023.ID_CONBE023
        LEFT JOIN conbe003 ON conbe023.ID_Produto = conbe003.ID_Produto

        -- Dados do ponto de venda
        INNER JOIN conve001 ON conve001.id_ponto_venda = conve002.id_ponto_venda

        -- Dados do plano de venda
        INNER JOIN conve041 ON conve041.id_plano_venda = conve002.id_plano_venda

        -- Dados do vendedor
        INNER JOIN conve014 ON conve014.id_comissionado = conve002.id_comissionado

    WHERE DATEDIFF(dd, CAST(conve002d.dt_cadastro AS DATE), CAST(GETDATE() AS DATE)) <= 30
),

final AS (
    SELECT * FROM propostas_nao_alocadas
    UNION ALL
    SELECT * FROM vendas
)

SELECT * FROM final
    