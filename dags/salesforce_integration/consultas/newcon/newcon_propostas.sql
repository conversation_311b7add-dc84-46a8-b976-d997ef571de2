-- =====================================================
-- CONSULTA: NewCon Propostas
-- DESCRIÇÃO: Extrai dados de propostas do sistema NewCon
-- FONTE: Banco NewCon (SQL Server)
-- =====================================================

SELECT
    'Proposta' AS end_point,
    concc030.id_documento AS IdProposta,
    corcc023.cd_inscricao_nacional AS CNPJCPF,
    corcc030.e_mail AS Email,
    LEFT(corcc023.nm_pessoa, CHARINDEX(' ', corcc023.nm_pessoa + ' ') - 1) AS PrimeiroNome,
    corcc023.nm_pessoa AS NomeCompleto,
    CONCAT('(55', ISNULL(ddd_telefone.ddd, ddd_endereco.ddd), corcc027.telefone, ')') AS Celular,

    -- Datas importantes
    CAST(concc030.DH_Inclusao AS DATE) AS dt_proposta,
    concc030.DH_Alteracao AS dt_ultimaModificacao,
    concc030.dt_adesao AS dt_fechamento,
    CAST(pagto_parc_1.dt_pagto_parcela_1 AS DATE) AS dt_pagamento,
    CAST(conve002d.dt_venda AS DATE) AS dt_venda,

    -- Data de validade calculada
    CASE
        WHEN DAY(concc030.dt_adesao) < 12 THEN CONVERT(VARCHAR(7), concc030.dt_adesao, 120) + '-12'
        WHEN DAY(concc030.dt_adesao) = 12 THEN concc030.dt_adesao
        ELSE CONVERT(VARCHAR(7), DATEADD(MONTH, 1, concc030.dt_adesao), 120) + '-12'
    END AS dt_validade,

    -- Status da proposta
    CASE
        WHEN concc030.st_situacao = 'C' THEN 'Cancelada'
        WHEN concc030.st_situacao = 'N' THEN 'Ativa'
    END AS Status_proposta,

    -- Status do contrato
    CASE
        WHEN conve002.id_cota IS NOT NULL THEN 'Assinado'
        ELSE 'Não assinado'
    END AS status_contrato,

    -- Valores e produtos
    CAST(concc030.vl_bem AS NUMERIC(18,2)) AS Valor,
    CONCAT(concc030.id_ponto_venda, '-', concc030.id_grupo_venda, '-', concc030.id_plano_venda, '-', concc030.id_bem) AS id_produto,

    -- Seguro
    CASE
        WHEN seguro.id_plano_seguro IS NOT NULL THEN 'Sim'
        ELSE 'Não'
    END AS seguro,

    -- Dados do ponto de venda e vendedor
    concc030.id_ponto_venda,
    conve001.CD_Ponto_Venda,
    conve001.nm_fantasia AS ponto_venda,
    conve014.nm_fantasia AS vendedor
FROM concc030

    -- Dados da pessoa
    INNER JOIN corcc023 ON corcc023.id_pessoa = concc030.id_pessoa

    -- Dados do documento/cota
    LEFT JOIN concc036 ON concc036.id_empresa = concc030.id_empresa
        AND concc036.id_tipo_documento = concc030.id_tipo_documento
        AND concc036.id_documento = concc030.id_documento

    -- Dados da venda (se existir)
    LEFT JOIN conve002 ON conve002.id_cota = concc036.id_cota
    LEFT JOIN conve002d ON conve002d.id_cota = conve002.id_cota

    -- Dados de endereço
    INNER JOIN corcc026 ON corcc026.id_pessoa = concc030.id_pessoa
        AND corcc026.id_endereco = concc030.id_endereco

    -- Dados de telefone
    INNER JOIN corcc027 ON corcc027.id_pessoa = concc030.id_pessoa
        AND corcc027.id_telefone = concc030.id_telefone

    -- DDDs para telefone e endereço
    INNER JOIN corcc015 ddd_endereco ON ddd_endereco.id_cidade = corcc026.id_cidade
    INNER JOIN corcc015 ddd_telefone ON ddd_telefone.id_cidade = corcc027.id_cidade
    -- Dados de pagamento da primeira parcela
    LEFT JOIN (
        SELECT
            id_cota,
            MIN(confi005.dt_pagamento) AS dt_pagto_parcela_1
        FROM confi005c
            INNER JOIN confi005 ON confi005.id_movimento_grupo = confi005c.id_movimento_grupo
        WHERE confi005.id_cd_movto_fin = 10
            AND confi005.id_movimento_estorno = 0
        GROUP BY id_cota
    ) pagto_parc_1 ON pagto_parc_1.id_cota = conve002.id_cota

    -- Dados de contato (duplicado - verificar necessidade)
    LEFT JOIN corcc030 ON corcc030.id_pessoa = concc030.id_pessoa
        AND corcc030.id_e_mail = concc030.id_e_mail

    -- Dados do ponto de venda
    INNER JOIN conve001 ON conve001.id_ponto_venda = concc030.id_ponto_venda

    -- Dados do vendedor
    INNER JOIN conve014 ON conve014.id_comissionado = concc030.id_comissionado

    -- Dados do produto/grupo
    INNER JOIN vw_GrProduto ON vw_GrProduto.id_grupo = concc030.id_grupo_venda
        AND vw_GrProduto.ID_Plano_Venda = concc030.ID_Plano_Venda
        AND vw_GrProduto.ID_Taxa_Plano = concc030.ID_Taxa_Plano

    -- Função para dados de seguro
    OUTER APPLY dbo.fn_dsvepeseguro(conve002.id_cota, GETDATE(), 10, 1) AS seguro

WHERE CAST(concc030.dt_adesao AS DATE) >=
    CASE
        WHEN DAY(GETDATE()) >= 13
        THEN DATEFROMPARTS(YEAR(GETDATE()), MONTH(GETDATE()), 13)
        ELSE DATEFROMPARTS(YEAR(GETDATE()), MONTH(GETDATE()) - 1, 13)
    END