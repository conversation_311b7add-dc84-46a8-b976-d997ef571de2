-- =====================================================
-- CONSULTA: Quiver Leads
-- DESCRIÇÃO: Extrai dados de leads do sistema Quiver
-- FONTE: <PERSON><PERSON> Quiver (SQL Server) via OpenQuery PostgreSQL
-- =====================================================

-- LEADS PESADOS
select *
from OpenQuery  (postgres, 
'
select
	e.id_evento,
	''Leads'' end_point	
	,case when e.id_statusevento = ''CONCLUIDO'' 
			then (select id_statusevento from syo_motivoevento where id_evento = e.id_evento order by dt_inc  desc limit 1)
			else e.id_statusevento
		end as status
	,(select ds_resultado from syo_acao where id_evento = e.id_evento order by id_acao desc limit 1) as resultado
	,nm_empresa
	,c.no_cpfcnpj as CNPJCPF
	,lower(c.ds_email) as Email
	,to_char(to_timestamp(e.dt_inc / 1000), ''YYYY-MM-DD'') as Dt_cadastro
	,concat(c.no_prefixocel, c.no_telefonecel) as Celular
	,''br'' Locale
	,split_part(c.nm_cliente, '' '', 1) as PrimeiroNome
	,c.nm_cliente as NomeCompleto
	,c.nm_cidaderes as Cidade
	,c.sg_ufres as Estado
	,''CGF Seguros'' TipoEmpresa
	,case when e.id_tipoevento in (''RENOVACAO'',''CGF RENOVACAO'')
		then coalesce((select ds_valor from public.syo_camposregistrointerface where ds_etiqueta = ''Ramo'' and id_registrointerface in (select coalesce(max(id_referencia), max(id_registrointerface)) from public.syo_registrointerface where id_evento = e.id_evento) limit 1),''Não informado'')
		else coalesce((select ds_valor from public.syo_camposregistrointerface where ds_etiqueta = ''Ramo'' and id_registrointerface in (select coalesce(max(id_referencia), max(id_registrointerface)) from public.syo_registrointerface where id_evento = e.id_evento) limit 1),''Riscos Diversos'')
	 end as TipoBem
	,coalesce((select ds_valor from public.syo_camposregistrointerface where ds_etiqueta = ''Tipo de negócio'' and id_registrointerface in (select coalesce(max(id_referencia), max(id_registrointerface)) from public.syo_registrointerface where id_evento = e.id_evento) limit 1),''Não informado'') as TipoSimulacao
	,coalesce((select ds_valor from public.syo_camposregistrointerface where ds_etiqueta = ''VALOR NEGOCIADO'' and id_registrointerface in (select coalesce(max(id_referencia), max(id_registrointerface)) from public.syo_registrointerface where id_evento = e.id_evento) limit 1),''Não informado'')  as ValorSimulacao
	,e.id_tipoevento as Campaign
	,e.ds_formacontato as Source
	,null Medium
	,e.ds_palavrachave as Term
	,null Content
	,nm_empresa as PontoVenda
	,null PlanoVenda
	,to_char(to_timestamp(c.dt_nascimento / 1000), ''YYYY-MM-DD'') as dt_nascimento
	,null Optin_consorcio_email
	,null Optin_consorcio_SMS
	,null Optin_consorcio_whatsapp
	,null Optin_seguros_email
	,null Optin_seguros_SMS
	,null Optin_seguros_wpp
	,null Optin_digital_email
	,null Optin_digital_SMS
	,null Optin_digital_whatsapp
	,null Optin_capital_email
	,null Optin_capital_SMS
	,null Optin_capitaL_whatsapp
	,to_char(to_timestamp(e.dt_conclusao / 1000), ''YYYY-MM-DD'') as Dt_simulacao
	,u.nm_usuario as NomeVendedor
from syo_evento e               
inner join syo_cliente c on c.id_cliente = e.id_cliente
inner join syo_empresa emp on emp.id_empresa = e.id_empresa
left join syo_usuario u on u.id_usuario = e.id_agente
where e.id_grupoevento = ''OPORTUNIDADE''    
	and e.id_tipoevento in  (''CGF'',''CGF - FROTA'',''CGF BAMAQ'',''CGF DIGITAL MKT'',''CGF DVM NF'',''CGF PERDIDOS'',''PROSPECCAO'',''CGF PROSPECCAO INSUCESSO'',''RENOVACAO'',''CGF RENOVACAO'',''VENDAS'')
	and coalesce(c.ds_email,'''') <> ''''
	and e.id_statusevento <> ''CANCELADO''
	and extract(day from current_date - to_timestamp(e.dt_inc / 1000))::int <= 30
--	and e.id_evento in (1009954, 1008732, 49518)
-- limit 10
')  

union all

-- LEADS AUTOMOTIVO
select *
from OpenQuery  (postgres, 
'
select
	e.id_evento,
	''Leads'' end_point	
	,case when e.id_statusevento = ''CONCLUIDO'' 
			then (select id_statusevento from syo_motivoevento where id_evento = e.id_evento order by dt_inc  desc limit 1)
			else e.id_statusevento
		end as status
	,(select ds_resultado from syo_acao where id_evento = e.id_evento order by id_acao desc limit 1) as resultado
	,nm_empresa
	,c.no_cpfcnpj as CNPJCPF
	,lower(c.ds_email) as Email
	,to_char(to_timestamp(e.dt_inc / 1000), ''YYYY-MM-DD'') as Dt_cadastro
	,concat(c.no_prefixocel, c.no_telefonecel) as Celular
	,''br'' Locale
	,split_part(c.nm_cliente, '' '', 1) as PrimeiroNome
	,c.nm_cliente as NomeCompleto
	,c.nm_cidaderes as Cidade
	,c.sg_ufres as Estado
	,''CGF Seguros'' TipoEmpresa
	,coalesce((select ds_valor from public.syo_camposregistrointerface where ds_etiqueta = ''Ramo'' and id_registrointerface in (select coalesce(max(id_referencia), max(id_registrointerface)) from public.syo_registrointerface where id_evento = e.id_evento) limit 1),''Não informado'') as TipoBem
	,coalesce((select ds_valor from public.syo_camposregistrointerface where ds_etiqueta = ''Tipo de negócio'' and id_registrointerface in (select coalesce(max(id_referencia), max(id_registrointerface)) from public.syo_registrointerface where id_evento = e.id_evento) limit 1),''Não informado'') as TipoSimulacao
	,coalesce((select ds_valor from public.syo_camposregistrointerface where ds_etiqueta = ''VALOR NEGOCIADO'' and id_registrointerface in (select coalesce(max(id_referencia), max(id_registrointerface)) from public.syo_registrointerface where id_evento = e.id_evento) limit 1),''Não informado'')  as ValorSimulacao
	,e.id_tipoevento as Campaign
	,e.ds_formacontato as Source
	,null Medium
	,e.ds_palavrachave as Term
	,null Content
	,nm_empresa as PontoVenda
	,null PlanoVenda
	,to_char(to_timestamp(c.dt_nascimento / 1000), ''YYYY-MM-DD'') as dt_nascimento
	,null Optin_consorcio_email
	,null Optin_consorcio_SMS
	,null Optin_consorcio_whatsapp
	,null Optin_seguros_email
	,null Optin_seguros_SMS
	,null Optin_seguros_wpp
	,null Optin_digital_email
	,null Optin_digital_SMS
	,null Optin_digital_whatsapp
	,null Optin_capital_email
	,null Optin_capital_SMS
	,null Optin_capitaL_whatsapp
	,to_char(to_timestamp(e.dt_conclusao / 1000), ''YYYY-MM-DD'') as Dt_simulacao
	,u.nm_usuario as NomeVendedor
from syo_evento e               
inner join syo_cliente c on c.id_cliente = e.id_cliente
inner join syo_empresa emp on emp.id_empresa = e.id_empresa
left join syo_usuario u on u.id_usuario = e.id_agente
where e.id_grupoevento = ''OPORTUNIDADE''    
	and e.id_tipoevento in  (''CGF AUTO PREMIUM NF'', ''CGF AUTO PREMIUM VENDAS'', ''CGF BAMAQ'', ''PROSPECCAO'', ''RENOVACAO'', ''CGF RENOVACAO'', ''CGF VENDAS AUTO PREMIUM'', ''CGF AUTO PREMIUM VENDAS'', ''CGF VENDAS PREMIUM - BAMAQ MERCEDES-BENZ BH'', ''CGF VENDAS PREMIUM - BAMAQ MERCEDES-BENZ JF'', ''CGF VENDAS PREMIUM - DELTA'', ''CGF VENDAS PREMIUM - GWM BH - PAMPULHA'', ''CGF VENDAS PREMIUM - GWM CAMPO GRANDE'', ''CGF VENDAS PREMIUM - GWM CONTAGEM'', ''CGF VENDAS PREMIUM - GWM DOURADOS'', ''CGF VENDAS PREMIUM - GWM BH'', ''CGF VENDAS PREMIUM - PORSCHE CAMPO GRANDE'', ''CGF VENDAS PREMIUM - PORSCHE CENTER BH'', ''CGF VENDAS PREMIUM - PORSCHE CENTER SALVADOR'')
	and coalesce(c.ds_email,'''') <> ''''
	and e.id_statusevento <> ''CANCELADO''	
	and extract(day from current_date - to_timestamp(e.dt_inc / 1000))::int <= 30
--	and e.id_evento in (1009954, 1008732, 49518)
')  

