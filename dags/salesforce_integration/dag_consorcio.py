"""
DAG Salesforce - Consórcio

Pipeline ETL especializado para a unidade de negócio Consórcio.
Processa dados de NewCon, RD Station e Orbbits para o Salesforce Marketing Cloud.

FONTES DE DADOS:
- NewCon: Clientes, Produtos, Leads, Propostas
- RD Station: Leads
- Orbbits: Origin, Payments, Sales, Prices, Proposals

DESTINO:
- Salesforce Marketing Cloud (Data Extensions específicas do Consórcio)

ARQUITETURA:
[10 Extrações Paralelas] → [Consolidação] → [4 Transformações] → [4 Carregamentos]

Autor: ETL Team
Data: 2025-01-17
Versão: 5.0 - Separação por Unidade de Negócio
Status: ✅ NOVA ARQUITETURA
"""

import os
import sys
import logging
from datetime import datetime

# Adicionar o diretório atual ao path para importações
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from dag_factory.dag_builder import create_consorcio_dag
except ImportError as e:
    logging.error(f"Erro ao importar DAG builder: {e}")
    # Fallback para evitar erro de importação no Airflow
    from airflow import DAG
    from airflow.operators.dummy_operator import DummyOperator
    
    def create_consorcio_dag(output_mode='salesforce'):
        """Fallback DAG em caso de erro de importação"""
        dag = DAG(
            'INTEGRACAO-SALESFORCE-CONSORCIO-FALLBACK',
            description='DAG de fallback - erro de importação',
            schedule_interval=None,
            start_date=datetime(2025, 1, 1),
            catchup=False,
            is_paused_upon_creation=True,
        )
        
        error_task = DummyOperator(
            task_id='import_error',
            dag=dag,
            doc_md="❌ Erro de importação - verifique os módulos dag_factory"
        )
        
        return dag

# =============================================================================
# CONFIGURAÇÃO DE MODO DE SAÍDA
# =============================================================================

# Configuração para alternar entre Salesforce e CSV
# Mude para 'csv' para gerar arquivos CSV ao invés de carregar no Salesforce
OUTPUT_MODE = os.getenv('SALESFORCE_OUTPUT_MODE_CONSORCIO', 'salesforce')  # 'salesforce' ou 'csv'

# Diretório dinâmico para salvar arquivos CSV (quando OUTPUT_MODE = 'csv')
# Usa o diretório da DAG + /carga_fria_consorcio por padrão
DEFAULT_CSV_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'carga_fria_consorcio')
CSV_OUTPUT_DIR = os.getenv('CSV_OUTPUT_DIR_CONSORCIO', DEFAULT_CSV_DIR)

def get_output_mode():
    """Retorna o modo de saída configurado para Consórcio"""
    return OUTPUT_MODE.lower()

def is_csv_mode():
    """Verifica se está no modo CSV para Consórcio"""
    return get_output_mode() == 'csv'

def is_salesforce_mode():
    """Verifica se está no modo Salesforce para Consórcio"""
    return get_output_mode() == 'salesforce'

# =============================================================================
# CRIAÇÃO DA DAG
# =============================================================================

# Cria a DAG usando o factory pattern
dag = create_consorcio_dag(output_mode=OUTPUT_MODE)

# Adiciona documentação específica do modo de saída
dag.doc_md = f"""
# ETL Salesforce Marketing Cloud - Consórcio

Pipeline ETL especializado para a unidade de negócio **Consórcio**.

## ⚙️ Configuração de Modo de Saída

**Modo atual: {OUTPUT_MODE.upper()}**

Este pipeline suporta dois modos de saída:
- **SALESFORCE**: Carrega dados diretamente no Salesforce Marketing Cloud
- **CSV**: Gera arquivos CSV para revisão/teste antes da carga

### Como Alterar o Modo:

1. **Via Variável de Ambiente:**
   ```bash
   export SALESFORCE_OUTPUT_MODE_CONSORCIO=csv        # Para modo CSV
   export SALESFORCE_OUTPUT_MODE_CONSORCIO=salesforce # Para modo Salesforce
   ```

2. **Via Código (linha 47):**
   ```python
   OUTPUT_MODE = 'csv'        # Para modo CSV
   OUTPUT_MODE = 'salesforce' # Para modo Salesforce
   ```

3. **Diretório CSV (personalizável):**
   ```bash
   export CSV_OUTPUT_DIR_CONSORCIO=/caminho/personalizado/para/csv
   ```
   
   **Padrão:** `<dag_directory>/carga_fria_consorcio/`

## Fontes de Dados - Consórcio:

### NewCon (4 Extrações)
- **Clientes**: ~73k registros
- **Produtos**: ~20k registros  
- **Leads**: Volume variável
- **Propostas**: ~533k registros

### RD Station (1 Extração)
- **Leads**: Volume variável com rate limiting

### Orbbits (5 Extrações)
- **Origin**: Dados de origem
- **Payments**: Dados de pagamentos
- **Sales**: Dados de vendas
- **Prices**: Dados de preços
- **Proposals**: Links de contratos PDF

## Arquitetura Paralela:

### Fase 1: Extração Paralela (10 Tarefas Simultâneas)
- Máximo paralelismo por fonte de dados
- Isolamento total de falhas
- Timeouts otimizados por volume

### Fase 2: Consolidação Rápida
- Unifica dados de 10 extrações independentes
- Validação de integridade

### Fase 3: Transformação Paralela (4 Tabelas)
- Produtos, Clientes, Leads, Propostas
- Processamento independente e otimizado

### Fase 4: Carregamento/Exportação Paralelo
- 4 Data Extensions simultâneas (modo Salesforce)
- 4 arquivos CSV com timestamp (modo CSV)

## Benefícios da Separação:
- ⚡ **Foco específico**: Apenas dados do Consórcio
- 🔧 **Isolamento**: Falhas não afetam Seguros
- 📊 **Observabilidade**: Logs específicos do Consórcio
- 🎯 **Escalabilidade**: Configuração independente
- 🔄 **Flexibilidade**: Modo CSV/Salesforce específico
- 🧪 **Testes**: Validação isolada por unidade
"""

# =============================================================================
# INSTRUÇÕES DE USO
# =============================================================================

# Para ativar a DAG:
# airflow dags unpause INTEGRACAO-SALESFORCE-CONSORCIO

# Para executar manualmente:
# airflow dags trigger INTEGRACAO-SALESFORCE-CONSORCIO

# Para monitorar execução:
# airflow dags state INTEGRACAO-SALESFORCE-CONSORCIO <execution_date>

# =============================================================================
# ALTERNÂNCIA ENTRE MODOS SALESFORCE E CSV - CONSÓRCIO
# =============================================================================

# MODO 1: SALESFORCE (padrão)
# - Carrega dados diretamente no Salesforce Marketing Cloud
# - Para usar: mantenha OUTPUT_MODE = 'salesforce' (linha 47)
# - Ou: export SALESFORCE_OUTPUT_MODE_CONSORCIO=salesforce

# MODO 2: CSV (para testes)
# - Gera arquivos CSV para revisão antes da carga
# - Para usar: altere OUTPUT_MODE = 'csv' (linha 47)
# - Ou: export SALESFORCE_OUTPUT_MODE_CONSORCIO=csv
# - Arquivos salvos em: <dag_directory>/carga_fria_consorcio/ (personalizável)

# EXEMPLO DE USO:
# 1. Teste primeiro com CSV:
#    export SALESFORCE_OUTPUT_MODE_CONSORCIO=csv
#    airflow dags trigger INTEGRACAO-SALESFORCE-CONSORCIO
#
# 2. Revise os arquivos CSV gerados em <dag_directory>/carga_fria_consorcio/
#
# 3. Se os dados estiverem corretos, mude para Salesforce:
#    export SALESFORCE_OUTPUT_MODE_CONSORCIO=salesforce
#    airflow dags trigger INTEGRACAO-SALESFORCE-CONSORCIO

# PERSONALIZAÇÃO DO DIRETÓRIO CSV:
# export CSV_OUTPUT_DIR_CONSORCIO=/caminho/personalizado/para/csv
