#!/usr/bin/env python3
"""
Script avançado para descobrir Data Extensions no Salesforce Marketing Cloud
Testa diferentes endpoints e métodos da API
"""

import os
import sys
import json
import requests
from datetime import datetime

# Adiciona o diretório atual ao path para importar módulos locais
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import SALESFORCE_CONFIG


class AdvancedDataExtensionLister:
    def __init__(self):
        self.client_id = SALESFORCE_CONFIG['client_id']
        self.client_secret = SALESFORCE_CONFIG['client_secret']
        self.auth_uri = SALESFORCE_CONFIG['auth_uri']
        self.rest_uri = SALESFORCE_CONFIG['rest_uri']
        self.access_token = None
        
    def authenticate(self):
        """Autentica no Salesforce Marketing Cloud"""
        auth_payload = {
            'grant_type': 'client_credentials',
            'client_id': self.client_id,
            'client_secret': self.client_secret
        }
        
        try:
            response = requests.post(
                f"{self.auth_uri}v2/token",
                json=auth_payload,
                timeout=30
            )
            
            if response.status_code == 200:
                token_data = response.json()
                self.access_token = token_data['access_token']
                print("✅ Autenticação realizada com sucesso")
                return True
            else:
                print(f"❌ Erro na autenticação: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Erro na autenticação: {e}")
            return False
    
    def test_specific_data_extension(self, external_key):
        """Testa se consegue acessar uma Data Extension específica usando PUT (como no código original)"""
        print(f"\n🎯 Testando acesso direto à Data Extension: {external_key}")
        
        if not self.access_token:
            print("❌ Token de acesso não disponível")
            return False
            
        headers = {
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json'
        }
        
        # Testa o endpoint exato que o código original usa para envio
        test_url = f"{self.rest_uri}data/v1/async/dataextensions/key:{external_key}/rows"
        
        # Payload de teste mínimo
        test_payload = {
            'items': [
                {'test_field': 'test_value'}
            ]
        }
        
        print(f"   🔗 URL: {test_url}")
        print(f"   📤 Método: PUT (como no envio real)")
        
        try:
            # Usa PUT como no código original
            response = requests.put(
                test_url,
                json=test_payload,
                headers=headers,
                timeout=30
            )
            
            print(f"   📊 Status: {response.status_code}")
            print(f"   📋 Resposta: {response.text}")
            
            if response.status_code == 202:  # Aceito para processamento assíncrono
                print(f"   ✅ Data Extension {external_key} EXISTE e aceita dados!")
                return True
            elif response.status_code == 400:
                response_text = response.text.lower()
                if 'customobjectnotfound' in response_text:
                    print(f"   ❌ Data Extension {external_key} NÃO EXISTE")
                    return False
                else:
                    print(f"   ⚠️  Data Extension existe mas há erro no payload")
                    return True
            else:
                print(f"   ❓ Status inesperado")
                return None
                
        except Exception as e:
            print(f"   💥 Erro: {e}")
            return None


def main():
    print("=" * 70)
    print("TESTE AVANÇADO: Descoberta de Data Extensions - Salesforce Marketing Cloud")
    print("=" * 70)
    print(f"Executado em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        lister = AdvancedDataExtensionLister()
        
        # 1. Autentica
        if not lister.authenticate():
            return
        
        print()
        print("=" * 70)
        print("TESTE DE ACESSO DIRETO (MÉTODO PUT)")
        print("=" * 70)
        
        # Testa acesso direto usando PUT como no código original
        test_keys = [
            ("tb_propostas (antigo)", "36B6F6B2-ECC3-4550-828C-5BF3B12FCBCA"),
            ("tb_propostas (novo)", "9FC20C5F-04EC-4B86-9491-D649ECFACAA5"),
            ("tb_clientes (antigo)", "B2B7ACFF-D5C8-4C86-B04A-DB4FBBA9198E"),
            ("tb_clientes (novo)", "6E536388-F5A4-416D-B0DE-AABE229F33C1"),
            ("tb_leads (antigo)", "EC0B7BFF-EC89-4A4D-914B-749F14B6F861"),
            ("tb_leads (novo)", "7C8AEDB6-64E8-41FC-965F-A4C37DF0ABE0"),
            ("tb_produtos (antigo)", "FCC1DCA7-D286-458D-BDCC-D050C1BA61A8"),
            ("tb_produtos (novo)", "19BEF707-C44F-4C3A-A9FB-53D2915258F1"),
        ]
        
        working_keys = []
        
        for name, key in test_keys:
            result = lister.test_specific_data_extension(key)
            if result is True:
                working_keys.append((name, key))
        
        print()
        print("=" * 70)
        print("RESUMO DOS RESULTADOS")
        print("=" * 70)
        
        if working_keys:
            print(f"✅ Data Extensions funcionais encontradas:")
            for name, key in working_keys:
                print(f"   - {name}: {key}")
        else:
            print("❌ Nenhuma Data Extension funcional encontrada")
        
        print()
        print("=" * 70)
        print("TESTE CONCLUÍDO")
        print("=" * 70)
        
    except Exception as e:
        print(f"❌ Erro geral: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()