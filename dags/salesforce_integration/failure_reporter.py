"""
ETL Consolidado - Relatório de Falhas
Sistema centralizado para coleta e relatório de falhas durante execução do ETL.
"""

import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

from salesforce_integration.utils import setup_logging

# =============================================================================
# CONFIGURAÇÃO DE LOGGING
# =============================================================================

logger = setup_logging()

# =============================================================================
# ENUMS E CLASSES DE DADOS  
# =============================================================================

class FailurePhase(Enum):
    """Fases do ETL onde podem ocorrer falhas"""
    EXTRACTION = "EXTRAÇÃO"
    TRANSFORMATION = "TRANSFORMAÇÃO" 
    LOADING = "CARREGAMENTO"
    VALIDATION = "VALIDAÇÃO"

class FailureSeverity(Enum):
    """Níveis de severidade das falhas"""
    CRITICAL = ("🔴", "CRÍTICO")
    ALERT = ("🟡", "ALERTA") 
    INFO = ("🔵", "INFO")
    
    def __init__(self, emoji, label):
        self.emoji = emoji
        self.label = label

@dataclass
class FailureRecord:
    """Registro individual de falha"""
    phase: FailurePhase
    severity: FailureSeverity
    table_name: str
    description: str
    context: Dict[str, Any]
    timestamp: datetime
    suggested_action: Optional[str] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

@dataclass
class ExecutionMetrics:
    """Métricas de execução para cada tabela"""
    table_name: str
    extracted_records: int = 0
    transformed_records: int = 0
    loaded_records: int = 0
    failed_batches: int = 0
    total_batches: int = 0
    error_messages: List[str] = None
    processing_errors: int = 0
    
    def __post_init__(self):
        if self.error_messages is None:
            self.error_messages = []

# =============================================================================
# CLASSE PRINCIPAL DE RELATÓRIO
# =============================================================================

class FailureReporter:
    """Sistema centralizado de coleta e relatório de falhas ETL"""
    
    def __init__(self):
        self.failures: List[FailureRecord] = []
        self.metrics: Dict[str, ExecutionMetrics] = {}
        self.start_time = datetime.now()
        self.end_time: Optional[datetime] = None
        self.logger = logging.getLogger(__name__)
    
    def add_failure(self, 
                   phase: FailurePhase,
                   severity: FailureSeverity, 
                   table_name: str,
                   description: str,
                   context: Dict[str, Any] = None,
                   suggested_action: str = None):
        """Adiciona um registro de falha"""
        
        failure = FailureRecord(
            phase=phase,
            severity=severity,
            table_name=table_name,
            description=description,
            context=context or {},
            timestamp=datetime.now(),
            suggested_action=suggested_action
        )
        
        self.failures.append(failure)
        self.logger.warning(f"Falha registrada: {phase.value} | {table_name} | {description}")
    
    def set_table_metrics(self, table_name: str, metrics: ExecutionMetrics):
        """Define métricas de execução para uma tabela"""
        self.metrics[table_name] = metrics
    
    
    def finalize_execution(self):
        """Finaliza coleta e prepara relatório"""
        self.end_time = datetime.now()
    
    def generate_report(self) -> str:
        """Gera relatório formatado com fluxo detalhado de dados"""
        if not self.end_time:
            self.finalize_execution()
        
        duration = self.end_time - self.start_time
        duration_str = f"{duration.total_seconds()/60:.0f}min"
        
        # Determina status geral baseado apenas em falhas reais
        critical_count = len([f for f in self.failures if f.severity == FailureSeverity.CRITICAL])
        status = "🔴 FALHA" if critical_count > 0 else ("🟡 ALERTA" if self.failures else "✅ SUCESSO")
        
        report = f"""
===== RELATÓRIO DE EXECUÇÃO ETL =====
📅 {self.start_time.strftime('%Y-%m-%d %H:%M:%S')} | ⏱️ Duração: {duration_str} | 📊 Status: {status}
"""
        
        # Seção de fluxo de dados por tabela
        report += "\n📊 FLUXO DE DADOS POR TABELA\n"

        # Mapeia fontes de dados por tabela
        data_sources = {
            'tb_produtos': '🏢 RD Station (Produtos)',
            'tb_clientes': '🏢 RD Station (Clientes)',
            'tb_leads': '🏢 RD Station (Leads)',
            'tb_propostas': '🏢 RD Station (Propostas)'
        }

        for table_name, metrics in self.metrics.items():
            # Ícone baseado em problemas reais de carregamento
            table_failures = [f for f in self.failures if f.table_name == table_name and f.phase == FailurePhase.LOADING]
            status_icon = "❌" if table_failures else "✅"

            # Mostra fonte dos dados
            source = data_sources.get(table_name, '📊 Fonte desconhecida')
            report += f"\n├─ {table_name.upper()}: {status_icon} ({source})\n"
            
            # Fluxo de dados
            if metrics.extracted_records > 0:
                report += f"│  ├─ 📥 Extração: {metrics.extracted_records:,} registros\n"
            if metrics.transformed_records > 0:
                lost_in_transform = metrics.extracted_records - metrics.transformed_records if metrics.extracted_records > 0 else 0
                if lost_in_transform > 0:
                    report += f"│  ├─ 🔄 Transformação: {metrics.transformed_records:,} registros (-{lost_in_transform:,} perdidos)\n"
                else:
                    report += f"│  ├─ 🔄 Transformação: {metrics.transformed_records:,} registros\n"
            if metrics.loaded_records > 0:
                lost_in_load = metrics.transformed_records - metrics.loaded_records if metrics.transformed_records > 0 else 0
                if lost_in_load > 0:
                    report += f"│  ├─ 📤 Carregamento: {metrics.loaded_records:,} registros (-{lost_in_load:,} perdidos)\n"
                else:
                    report += f"│  ├─ 📤 Carregamento: {metrics.loaded_records:,} registros\n"
            
            # Detalhes de lotes se houver problemas
            if metrics.failed_batches > 0:
                success_rate = ((metrics.total_batches - metrics.failed_batches) / metrics.total_batches * 100) if metrics.total_batches > 0 else 0
                report += f"│  ├─ 🚨 Lotes: {metrics.failed_batches} falharam de {metrics.total_batches} total ({success_rate:.1f}% sucesso)\n"
                
                # Detalhamento dos erros específicos
                if metrics.error_messages:
                    error_types = {}
                    for error in metrics.error_messages:
                        error_str = str(error).lower()
                        if 'email' in error_str and ('invalid' in error_str or 'parse error' in error_str):
                            error_types['Emails inválidos'] = error_types.get('Emails inválidos', 0) + 1
                        elif 'exceeds' in error_str and 'column' in error_str:
                            error_types['Campos muito longos'] = error_types.get('Campos muito longos', 0) + 1
                        elif 'required' in error_str:
                            error_types['Campos obrigatórios'] = error_types.get('Campos obrigatórios', 0) + 1
                        elif 'duplicate' in error_str:
                            error_types['Registros duplicados'] = error_types.get('Registros duplicados', 0) + 1
                        else:
                            error_types['Outros erros'] = error_types.get('Outros erros', 0) + 1
                    
                    # Mostra os tipos de erro mais frequentes
                    sorted_errors = sorted(error_types.items(), key=lambda x: x[1], reverse=True)
                    for i, (error_type, count) in enumerate(sorted_errors[:3]):
                        connector = "│  │  ├─" if i < len(sorted_errors[:3]) - 1 else "│  │  └─"
                        report += f"{connector} {error_type}: {count} ocorrências\n"
            else:
                if metrics.total_batches > 0:
                    report += f"│  └─ ✅ Lotes: {metrics.total_batches} todos bem-sucedidos\n"
        
        # Seção de problemas críticos se houver
        critical_failures = [f for f in self.failures if f.severity == FailureSeverity.CRITICAL]
        if critical_failures:
            report += "\n🚨 PROBLEMAS CRÍTICOS ENCONTRADOS:\n"
            for i, failure in enumerate(critical_failures):
                connector = "├─" if i < len(critical_failures) - 1 else "└─"
                report += f"{connector} {failure.table_name}: {failure.description}\n"

                # Mostra erros detalhados dos lotes se disponíveis
                if failure.table_name in self.metrics:
                    metrics = self.metrics[failure.table_name]
                    if hasattr(metrics, 'error_messages') and metrics.error_messages:
                        indent = "│  " if i < len(critical_failures) - 1 else "   "
                        report += f"{indent}📋 DETALHES DOS ERROS:\n"

                        # Agrupa erros similares
                        error_groups = {}
                        for error_msg in metrics.error_messages[:10]:  # Limita a 10 erros
                            # Extrai o tipo de erro principal
                            if "InvalidEmailAddress" in error_msg:
                                error_type = "Email inválido"
                            elif "exceeds the column" in error_msg:
                                error_type = "Campo excede tamanho"
                            elif "required" in error_msg.lower():
                                error_type = "Campo obrigatório"
                            elif "Item" in error_msg and ":" in error_msg:
                                # Extrai o erro específico após ":"
                                error_type = error_msg.split(":", 2)[-1].strip() if ":" in error_msg else error_msg
                            else:
                                error_type = "Erro de validação"

                            if error_type not in error_groups:
                                error_groups[error_type] = []
                            error_groups[error_type].append(error_msg)

                        # Mostra erros agrupados
                        for error_type, errors in error_groups.items():
                            report += f"{indent}  • {error_type} ({len(errors)} ocorrências)\n"
                            # Mostra alguns exemplos específicos
                            for example in errors[:3]:  # Máximo 3 exemplos por tipo
                                if "Lote" in example and "falhou:" in example:
                                    # Extrai ID do lote e erro específico
                                    parts = example.split("falhou:", 1)
                                    if len(parts) == 2:
                                        batch_info = parts[0].strip()
                                        error_detail = parts[1].strip()
                                        report += f"{indent}    - {batch_info}: {error_detail[:100]}{'...' if len(error_detail) > 100 else ''}\n"
                                else:
                                    report += f"{indent}    - {example[:100]}{'...' if len(example) > 100 else ''}\n"

                            if len(errors) > 3:
                                report += f"{indent}    ... e mais {len(errors) - 3} erros similares\n"

                if failure.suggested_action:
                    indent = "│  " if i < len(critical_failures) - 1 else "   "
                    report += f"{indent}💡 Ação: {failure.suggested_action}\n"
        
        # Seção de alertas se houver
        alert_failures = [f for f in self.failures if f.severity == FailureSeverity.ALERT]
        if alert_failures:
            report += "\n⚠️ ALERTAS:\n"
            for i, failure in enumerate(alert_failures):
                connector = "├─" if i < len(alert_failures) - 1 else "└─"
                report += f"{connector} {failure.table_name}: {failure.description}\n"

                # Mostra alguns erros de exemplo para alertas também
                if failure.table_name in self.metrics:
                    metrics = self.metrics[failure.table_name]
                    if hasattr(metrics, 'error_messages') and metrics.error_messages:
                        indent = "│  " if i < len(alert_failures) - 1 else "   "
                        # Para alertas, mostra apenas os primeiros 3 erros como exemplo
                        report += f"{indent}📋 Exemplos de erros:\n"
                        for error_msg in metrics.error_messages[:3]:
                            if "Lote" in error_msg and "falhou:" in error_msg:
                                parts = error_msg.split("falhou:", 1)
                                if len(parts) == 2:
                                    batch_info = parts[0].strip()
                                    error_detail = parts[1].strip()
                                    report += f"{indent}  • {batch_info}: {error_detail[:80]}{'...' if len(error_detail) > 80 else ''}\n"
                            else:
                                report += f"{indent}  • {error_msg[:80]}{'...' if len(error_msg) > 80 else ''}\n"

                        if len(metrics.error_messages) > 3:
                            report += f"{indent}  ... e mais {len(metrics.error_messages) - 3} erros\n"

                if failure.suggested_action:
                    indent = "│  " if i < len(alert_failures) - 1 else "   "
                    report += f"{indent}💡 Ação: {failure.suggested_action}\n"
        
        # Resumo final
        total_extracted = sum(m.extracted_records for m in self.metrics.values())
        total_loaded = sum(m.loaded_records for m in self.metrics.values())
        total_lost = total_extracted - total_loaded if total_extracted > 0 else 0
        
        report += f"\n🎯 RESUMO FINAL:\n"
        report += f"├─ Total extraído: {total_extracted:,} registros\n"
        report += f"├─ Total carregado: {total_loaded:,} registros\n"
        if total_lost > 0:
            loss_rate = (total_lost / total_extracted * 100) if total_extracted > 0 else 0
            report += f"├─ Perdas no processo: {total_lost:,} registros ({loss_rate:.1f}%)\n"
        
        if critical_failures:
            report += f"└─ Status: 🔴 REQUER ATENÇÃO IMEDIATA - {len(critical_failures)} problemas críticos\n"
        elif alert_failures:
            report += f"└─ Status: 🟡 MONITORAR - {len(alert_failures)} alertas\n"
        else:
            report += f"└─ Status: ✅ PIPELINE SAUDÁVEL - sem problemas detectados\n"
        
        return report
    
    def get_failure_summary(self) -> Dict[str, Any]:
        """Retorna resumo das falhas para integração"""
        return {
            'total_failures': len(self.failures),
            'critical_count': len([f for f in self.failures if f.severity == FailureSeverity.CRITICAL]),
            'alert_count': len([f for f in self.failures if f.severity == FailureSeverity.ALERT]),
            'tables_with_issues': list(set(f.table_name for f in self.failures)),
            'duration_minutes': (self.end_time - self.start_time).total_seconds() / 60 if self.end_time else 0
        }