"""
DAG Salesforce - Seguros

Pipeline ETL especializado para a unidade de negócio Seguros.
Processa dados do Quiver para o Salesforce Marketing Cloud.

FONTES DE DADOS:
- Quiver: Clientes, Produtos, Leads, Propostas

DESTINO:
- Salesforce Marketing Cloud (Data Extensions específicas de Seguros)

ARQUITETURA:
[4 Extrações Paralelas] → [Consolidação] → [4 Transformações] → [4 Carregamentos]

Autor: ETL Team
Data: 2025-01-17
Versão: 5.0 - Separação por Unidade de Negócio
Status: ✅ NOVA ARQUITETURA
"""

import os
import sys
import logging
from datetime import datetime

# Adicionar o diretório atual ao path para importações
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from dag_factory.dag_builder import create_seguros_dag
except ImportError as e:
    logging.error(f"Erro ao importar DAG builder: {e}")
    # Fallback para evitar erro de importação no Airflow
    from airflow import DAG
    from airflow.operators.dummy_operator import DummyOperator
    
    def create_seguros_dag(output_mode='salesforce'):
        """Fallback DAG em caso de erro de importação"""
        dag = DAG(
            'INTEGRACAO-SALESFORCE-SEGUROS-FALLBACK',
            description='DAG de fallback - erro de importação',
            schedule_interval=None,
            start_date=datetime(2025, 1, 1),
            catchup=False,
            is_paused_upon_creation=True,
        )
        
        error_task = DummyOperator(
            task_id='import_error',
            dag=dag,
            doc_md="❌ Erro de importação - verifique os módulos dag_factory"
        )
        
        return dag

# =============================================================================
# CONFIGURAÇÃO DE MODO DE SAÍDA
# =============================================================================

# Configuração para alternar entre Salesforce e CSV
# Mude para 'csv' para gerar arquivos CSV ao invés de carregar no Salesforce
OUTPUT_MODE = os.getenv('SALESFORCE_OUTPUT_MODE_SEGUROS', 'salesforce')  # 'salesforce' ou 'csv'

# Diretório dinâmico para salvar arquivos CSV (quando OUTPUT_MODE = 'csv')
# Usa o diretório da DAG + /carga_fria_seguros por padrão
DEFAULT_CSV_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'carga_fria_seguros')
CSV_OUTPUT_DIR = os.getenv('CSV_OUTPUT_DIR_SEGUROS', DEFAULT_CSV_DIR)

def get_output_mode():
    """Retorna o modo de saída configurado para Seguros"""
    return OUTPUT_MODE.lower()

def is_csv_mode():
    """Verifica se está no modo CSV para Seguros"""
    return get_output_mode() == 'csv'

def is_salesforce_mode():
    """Verifica se está no modo Salesforce para Seguros"""
    return get_output_mode() == 'salesforce'

# =============================================================================
# CRIAÇÃO DA DAG
# =============================================================================

# Cria a DAG usando o factory pattern
dag = create_seguros_dag(output_mode=OUTPUT_MODE)

# Adiciona documentação específica do modo de saída
dag.doc_md = f"""
# ETL Salesforce Marketing Cloud - Seguros

Pipeline ETL especializado para a unidade de negócio **Seguros**.

## ⚙️ Configuração de Modo de Saída

**Modo atual: {OUTPUT_MODE.upper()}**

Este pipeline suporta dois modos de saída:
- **SALESFORCE**: Carrega dados diretamente no Salesforce Marketing Cloud
- **CSV**: Gera arquivos CSV para revisão/teste antes da carga

### Como Alterar o Modo:

1. **Via Variável de Ambiente:**
   ```bash
   export SALESFORCE_OUTPUT_MODE_SEGUROS=csv        # Para modo CSV
   export SALESFORCE_OUTPUT_MODE_SEGUROS=salesforce # Para modo Salesforce
   ```

2. **Via Código (linha 47):**
   ```python
   OUTPUT_MODE = 'csv'        # Para modo CSV
   OUTPUT_MODE = 'salesforce' # Para modo Salesforce
   ```

3. **Diretório CSV (personalizável):**
   ```bash
   export CSV_OUTPUT_DIR_SEGUROS=/caminho/personalizado/para/csv
   ```
   
   **Padrão:** `<dag_directory>/carga_fria_seguros/`

## Fontes de Dados - Seguros:

### Quiver (4 Extrações)
- **Clientes**: ~20k registros
- **Produtos**: ~5k registros  
- **Leads**: Volume variável
- **Propostas**: ~100k registros

## Arquitetura Otimizada:

### Fase 1: Extração Paralela (4 Tarefas Simultâneas)
- Paralelismo otimizado para Quiver
- Isolamento total de falhas
- Timeouts ajustados para menor volume

### Fase 2: Consolidação Rápida
- Unifica dados de 4 extrações independentes
- Validação de integridade específica

### Fase 3: Transformação Paralela (4 Tabelas)
- Produtos, Clientes, Leads, Propostas
- Processamento otimizado para dados de Seguros

### Fase 4: Carregamento/Exportação Paralelo
- 4 Data Extensions específicas de Seguros (modo Salesforce)
- 4 arquivos CSV com timestamp (modo CSV)

## Benefícios da Separação:
- ⚡ **Foco específico**: Apenas dados de Seguros
- 🔧 **Isolamento**: Falhas não afetam Consórcio
- 📊 **Observabilidade**: Logs específicos de Seguros
- 🎯 **Escalabilidade**: Configuração independente
- 🔄 **Flexibilidade**: Modo CSV/Salesforce específico
- 🧪 **Testes**: Validação isolada por unidade
- ⏱️ **Performance**: Otimizado para menor volume de dados

## Vantagens para Seguros:
- **Execução mais rápida**: Apenas 4 extrações vs 14 da DAG unificada
- **Menor consumo de recursos**: Processamento focado
- **Maior frequência**: Pode executar mais vezes por dia
- **Troubleshooting simplificado**: Logs específicos do Quiver
- **Configuração independente**: SLA e timeouts otimizados
"""

# =============================================================================
# INSTRUÇÕES DE USO
# =============================================================================

# Para ativar a DAG:
# airflow dags unpause INTEGRACAO-SALESFORCE-SEGUROS

# Para executar manualmente:
# airflow dags trigger INTEGRACAO-SALESFORCE-SEGUROS

# Para monitorar execução:
# airflow dags state INTEGRACAO-SALESFORCE-SEGUROS <execution_date>

# =============================================================================
# ALTERNÂNCIA ENTRE MODOS SALESFORCE E CSV - SEGUROS
# =============================================================================

# MODO 1: SALESFORCE (padrão)
# - Carrega dados diretamente no Salesforce Marketing Cloud
# - Para usar: mantenha OUTPUT_MODE = 'salesforce' (linha 47)
# - Ou: export SALESFORCE_OUTPUT_MODE_SEGUROS=salesforce

# MODO 2: CSV (para testes)
# - Gera arquivos CSV para revisão antes da carga
# - Para usar: altere OUTPUT_MODE = 'csv' (linha 47)
# - Ou: export SALESFORCE_OUTPUT_MODE_SEGUROS=csv
# - Arquivos salvos em: <dag_directory>/carga_fria_seguros/ (personalizável)

# EXEMPLO DE USO:
# 1. Teste primeiro com CSV:
#    export SALESFORCE_OUTPUT_MODE_SEGUROS=csv
#    airflow dags trigger INTEGRACAO-SALESFORCE-SEGUROS
#
# 2. Revise os arquivos CSV gerados em <dag_directory>/carga_fria_seguros/
#
# 3. Se os dados estiverem corretos, mude para Salesforce:
#    export SALESFORCE_OUTPUT_MODE_SEGUROS=salesforce
#    airflow dags trigger INTEGRACAO-SALESFORCE-SEGUROS

# PERSONALIZAÇÃO DO DIRETÓRIO CSV:
# export CSV_OUTPUT_DIR_SEGUROS=/caminho/personalizado/para/csv

# =============================================================================
# CONFIGURAÇÕES ESPECÍFICAS DE SEGUROS
# =============================================================================

# Horário otimizado: 9h (1 hora após Consórcio)
# SLA reduzido: 1 hora (vs 2 horas do Consórcio)
# Timeouts menores: Ajustados para menor volume de dados
# Prioridade: Pode executar com maior frequência se necessário
