"""
Transformações por Unidade de Negócio

Implementa transformações específicas para cada unidade de negócio:
- Consórcio: NewCon + RD Station + Orbbits (SEM Quiver)
- Seguros: Quiver (SEM NewCon/RD Station/Orbbits)

Esta separação resolve o problema do UNION ALL entre unidades de negócio,
permitindo que cada DAG processe apenas seus próprios dados.
"""

import pandas as pd
import numpy as np
import logging
import sys
import os
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple

# Adicionar o diretório atual ao path para importações
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# Importar funções de transformação existentes para reutilização
try:
    from data_transformers import (
    # Funções de preparação que serão reutilizadas
    _prepare_newcon_for_clients,
    _prepare_orbbits_for_clients,
    _prepare_quiver_for_clients,
    _prepare_newcon_for_leads,
    _prepare_rdstation_for_leads,
    _prepare_orbbits_for_leads,
    _prepare_quiver_for_leads,
    _prepare_newcon_for_products,
    _prepare_orbbits_for_products,
    _prepare_quiver_for_products,
    _prepare_newcon_for_proposals,
    _prepare_orbbits_for_proposals,
    _prepare_quiver_for_proposals,
    
    # Funções de validação e limpeza
    _safe_clean_fields,
    _prepare_dataframe_for_serialization,
    validate_required_fields,
    format_data_for_salesforce,
    
    # Constantes
    REQUIRED_FIELDS
    )
except ImportError as import_error:
    error_msg = str(import_error)
    logging.error(f"Erro ao importar data_transformers: {error_msg}")

    # Criar função de fallback que não depende de variáveis externas
    def create_fallback_function(function_name):
        def fallback_function(*args, **kwargs):
            raise ImportError(f"Função {function_name} não disponível - data_transformers não pôde ser importado: {error_msg}")
        return fallback_function

    # Atribuir fallback para todas as funções necessárias
    _prepare_newcon_for_clients = create_fallback_function('_prepare_newcon_for_clients')
    _prepare_orbbits_for_clients = create_fallback_function('_prepare_orbbits_for_clients')
    _prepare_quiver_for_clients = create_fallback_function('_prepare_quiver_for_clients')
    _prepare_newcon_for_leads = create_fallback_function('_prepare_newcon_for_leads')
    _prepare_rdstation_for_leads = create_fallback_function('_prepare_rdstation_for_leads')
    _prepare_orbbits_for_leads = create_fallback_function('_prepare_orbbits_for_leads')
    _prepare_quiver_for_leads = create_fallback_function('_prepare_quiver_for_leads')
    _prepare_newcon_for_products = create_fallback_function('_prepare_newcon_for_products')
    _prepare_orbbits_for_products = create_fallback_function('_prepare_orbbits_for_products')
    _prepare_quiver_for_products = create_fallback_function('_prepare_quiver_for_products')
    _prepare_newcon_for_proposals = create_fallback_function('_prepare_newcon_for_proposals')
    _prepare_orbbits_for_proposals = create_fallback_function('_prepare_orbbits_for_proposals')
    _prepare_quiver_for_proposals = create_fallback_function('_prepare_quiver_for_proposals')
    _safe_clean_fields = create_fallback_function('_safe_clean_fields')
    _prepare_dataframe_for_serialization = create_fallback_function('_prepare_dataframe_for_serialization')
    validate_required_fields = create_fallback_function('validate_required_fields')
    format_data_for_salesforce = create_fallback_function('format_data_for_salesforce')
    REQUIRED_FIELDS = {}

try:
    from utils import setup_logging
    from config import DATA_EXTENSIONS
except ImportError as config_error:
    logging.error(f"Erro ao importar utils/config: {config_error}")
    # Fallbacks para logging e config
    def setup_logging():
        return logging.getLogger(__name__)
    DATA_EXTENSIONS = {}

# =============================================================================
# CONFIGURAÇÃO DE LOGGING
# =============================================================================

logger = setup_logging()

# =============================================================================
# TRANSFORMAÇÕES ESPECÍFICAS DO CONSÓRCIO
# =============================================================================

def transform_clientes_consorcio(extracted_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
    """
    Transforma dados de clientes APENAS do Consórcio (NewCon + Orbbits).
    
    FONTES PROCESSADAS:
    - newcon_clients
    - orbbits_origin
    
    FONTES IGNORADAS:
    - quiver_clients (processado separadamente em Seguros)
    
    Args:
        extracted_data: Dict com DataFrames extraídos
        
    Returns:
        Dict com DataFrame transformado: {'tb_clientes': df_clientes_consorcio}
    """
    logger.info("🏢 Iniciando transformação de clientes - CONSÓRCIO")
    
    try:
        # Extrair dados apenas do Consórcio
        newcon_data = extracted_data.get('newcon_clients', pd.DataFrame())
        orbbits_data = extracted_data.get('orbbits_origin', pd.DataFrame())
        
        logger.info(f"Dados Consórcio - NewCon: {len(newcon_data)} registros")
        logger.info(f"Dados Consórcio - Orbbits: {len(orbbits_data)} registros")
        
        # Processar NewCon
        if not newcon_data.empty:
            df_clients_consorcio = _prepare_newcon_for_clients(newcon_data)
            logger.info(f"NewCon processado: {len(df_clients_consorcio)} registros")
        else:
            df_clients_consorcio = pd.DataFrame()
            logger.warning("NewCon clients vazio - criando DataFrame vazio")
        
        # Processar e consolidar Orbbits
        if not orbbits_data.empty:
            df_orbbits = _prepare_orbbits_for_clients(orbbits_data)
            
            if not df_clients_consorcio.empty:
                # Consolidar NewCon + Orbbits
                df_clients_consorcio = pd.concat([df_clients_consorcio, df_orbbits], ignore_index=True, sort=False)
                logger.info(f"Consolidação NewCon+Orbbits: {len(df_clients_consorcio)} registros")
            else:
                df_clients_consorcio = df_orbbits
                logger.info(f"Usando apenas Orbbits: {len(df_clients_consorcio)} registros")
        else:
            logger.info("Orbbits origin vazio - usando apenas NewCon")
        
        # Validações e limpeza
        if not df_clients_consorcio.empty:
            df_clients_consorcio = _safe_clean_fields(df_clients_consorcio)
            df_clients_consorcio = validate_required_fields(df_clients_consorcio, 'tb_clientes')
            df_clients_consorcio = format_data_for_salesforce(df_clients_consorcio, 'tb_clientes')
            df_clients_consorcio = _prepare_dataframe_for_serialization(df_clients_consorcio)
            
            logger.info(f"✅ Clientes Consórcio transformados: {len(df_clients_consorcio)} registros finais")
        else:
            logger.warning("⚠️ Nenhum dado de clientes do Consórcio para processar")
        
        return {'tb_clientes': df_clients_consorcio}
        
    except Exception as e:
        logger.error(f"❌ Erro na transformação de clientes Consórcio: {e}")
        raise

def transform_leads_consorcio(extracted_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
    """
    Transforma dados de leads APENAS do Consórcio (NewCon + RD Station + Orbbits).
    
    FONTES PROCESSADAS:
    - newcon_leads
    - rdstation_leads
    - orbbits_* (origin, payments, sales, prices)
    
    FONTES IGNORADAS:
    - quiver_leads (processado separadamente em Seguros)
    
    Args:
        extracted_data: Dict com DataFrames extraídos
        
    Returns:
        Dict com DataFrame transformado: {'tb_leads': df_leads_consorcio}
    """
    logger.info("🏢 Iniciando transformação de leads - CONSÓRCIO")
    
    try:
        # Extrair dados apenas do Consórcio
        newcon_data = extracted_data.get('newcon_leads', pd.DataFrame())
        rdstation_data = extracted_data.get('rdstation_leads', pd.DataFrame())
        orbbits_origin = extracted_data.get('orbbits_origin', pd.DataFrame())
        orbbits_payments = extracted_data.get('orbbits_payments', pd.DataFrame())
        orbbits_sales = extracted_data.get('orbbits_sales', pd.DataFrame())
        orbbits_prices = extracted_data.get('orbbits_prices', pd.DataFrame())
        
        logger.info(f"Dados Consórcio - NewCon: {len(newcon_data)} registros")
        logger.info(f"Dados Consórcio - RD Station: {len(rdstation_data)} registros")
        logger.info(f"Dados Consórcio - Orbbits Origin: {len(orbbits_origin)} registros")
        
        # Processar NewCon
        if not newcon_data.empty:
            df_leads_consorcio = _prepare_newcon_for_leads(newcon_data)
            logger.info(f"NewCon processado: {len(df_leads_consorcio)} registros")
        else:
            df_leads_consorcio = pd.DataFrame()
            logger.warning("NewCon leads vazio - criando DataFrame vazio")
        
        # Processar e consolidar RD Station
        if not rdstation_data.empty:
            df_rdstation = _prepare_rdstation_for_leads(rdstation_data)
            
            if not df_leads_consorcio.empty:
                df_leads_consorcio = pd.concat([df_leads_consorcio, df_rdstation], ignore_index=True, sort=False)
                logger.info(f"Consolidação NewCon+RDStation: {len(df_leads_consorcio)} registros")
            else:
                df_leads_consorcio = df_rdstation
                logger.info(f"Usando apenas RD Station: {len(df_leads_consorcio)} registros")
        else:
            logger.info("RD Station leads vazio - usando apenas NewCon")
        
        # Processar e consolidar Orbbits (todos os tipos)
        orbbits_combined = {
            'orbbits_origin': orbbits_origin,
            'orbbits_payments': orbbits_payments,
            'orbbits_sales': orbbits_sales,
            'orbbits_prices': orbbits_prices
        }
        
        for orbbits_type, orbbits_data in orbbits_combined.items():
            if not orbbits_data.empty:
                df_orbbits = _prepare_orbbits_for_leads(orbbits_data)
                
                if not df_leads_consorcio.empty:
                    df_leads_consorcio = pd.concat([df_leads_consorcio, df_orbbits], ignore_index=True, sort=False)
                    logger.info(f"Consolidação com {orbbits_type}: {len(df_leads_consorcio)} registros")
                else:
                    df_leads_consorcio = df_orbbits
                    logger.info(f"Usando apenas {orbbits_type}: {len(df_leads_consorcio)} registros")
        
        # Validações e limpeza
        if not df_leads_consorcio.empty:
            df_leads_consorcio = _safe_clean_fields(df_leads_consorcio)
            df_leads_consorcio = validate_required_fields(df_leads_consorcio, 'tb_leads')
            df_leads_consorcio = format_data_for_salesforce(df_leads_consorcio, 'tb_leads')
            df_leads_consorcio = _prepare_dataframe_for_serialization(df_leads_consorcio)
            
            logger.info(f"✅ Leads Consórcio transformados: {len(df_leads_consorcio)} registros finais")
        else:
            logger.warning("⚠️ Nenhum dado de leads do Consórcio para processar")
        
        return {'tb_leads': df_leads_consorcio}
        
    except Exception as e:
        logger.error(f"❌ Erro na transformação de leads Consórcio: {e}")
        raise

def transform_produtos_consorcio(extracted_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
    """
    Transforma dados de produtos APENAS do Consórcio (NewCon + Orbbits).
    
    FONTES PROCESSADAS:
    - newcon_products
    - orbbits_prices
    
    FONTES IGNORADAS:
    - quiver_products (processado separadamente em Seguros)
    
    Args:
        extracted_data: Dict com DataFrames extraídos
        
    Returns:
        Dict com DataFrame transformado: {'tb_produtos': df_produtos_consorcio}
    """
    logger.info("🏢 Iniciando transformação de produtos - CONSÓRCIO")
    
    try:
        # Extrair dados apenas do Consórcio
        newcon_data = extracted_data.get('newcon_products', pd.DataFrame())
        orbbits_data = extracted_data.get('orbbits_prices', pd.DataFrame())
        
        logger.info(f"Dados Consórcio - NewCon: {len(newcon_data)} registros")
        logger.info(f"Dados Consórcio - Orbbits: {len(orbbits_data)} registros")
        
        # Processar NewCon
        if not newcon_data.empty:
            df_produtos_consorcio = _prepare_newcon_for_products(newcon_data)
            logger.info(f"NewCon processado: {len(df_produtos_consorcio)} registros")
        else:
            df_produtos_consorcio = pd.DataFrame()
            logger.warning("NewCon products vazio - criando DataFrame vazio")
        
        # Processar e consolidar Orbbits
        if not orbbits_data.empty:
            df_orbbits = _prepare_orbbits_for_products(orbbits_data)
            
            if not df_produtos_consorcio.empty:
                df_produtos_consorcio = pd.concat([df_produtos_consorcio, df_orbbits], ignore_index=True, sort=False)
                logger.info(f"Consolidação NewCon+Orbbits: {len(df_produtos_consorcio)} registros")
            else:
                df_produtos_consorcio = df_orbbits
                logger.info(f"Usando apenas Orbbits: {len(df_produtos_consorcio)} registros")
        else:
            logger.info("Orbbits prices vazio - usando apenas NewCon")
        
        # Validações e limpeza
        if not df_produtos_consorcio.empty:
            df_produtos_consorcio = _safe_clean_fields(df_produtos_consorcio)
            df_produtos_consorcio = validate_required_fields(df_produtos_consorcio, 'tb_produtos')
            df_produtos_consorcio = format_data_for_salesforce(df_produtos_consorcio, 'tb_produtos')
            df_produtos_consorcio = _prepare_dataframe_for_serialization(df_produtos_consorcio)
            
            logger.info(f"✅ Produtos Consórcio transformados: {len(df_produtos_consorcio)} registros finais")
        else:
            logger.warning("⚠️ Nenhum dado de produtos do Consórcio para processar")
        
        return {'tb_produtos': df_produtos_consorcio}
        
    except Exception as e:
        logger.error(f"❌ Erro na transformação de produtos Consórcio: {e}")
        raise

def transform_propostas_consorcio(extracted_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
    """
    Transforma dados de propostas APENAS do Consórcio (NewCon + Orbbits).
    
    FONTES PROCESSADAS:
    - newcon_proposals
    - orbbits_* (payments, sales, proposals)
    
    FONTES IGNORADAS:
    - quiver_proposals (processado separadamente em Seguros)
    
    Args:
        extracted_data: Dict com DataFrames extraídos
        
    Returns:
        Dict com DataFrame transformado: {'tb_propostas': df_propostas_consorcio}
    """
    logger.info("🏢 Iniciando transformação de propostas - CONSÓRCIO")
    
    try:
        # Extrair dados apenas do Consórcio
        newcon_data = extracted_data.get('newcon_proposals', pd.DataFrame())
        orbbits_payments = extracted_data.get('orbbits_payments', pd.DataFrame())
        orbbits_sales = extracted_data.get('orbbits_sales', pd.DataFrame())
        orbbits_proposals = extracted_data.get('orbbits_proposals', pd.DataFrame())
        
        logger.info(f"Dados Consórcio - NewCon: {len(newcon_data)} registros")
        logger.info(f"Dados Consórcio - Orbbits Payments: {len(orbbits_payments)} registros")
        logger.info(f"Dados Consórcio - Orbbits Sales: {len(orbbits_sales)} registros")
        logger.info(f"Dados Consórcio - Orbbits Proposals: {len(orbbits_proposals)} registros")
        
        # Processar NewCon
        if not newcon_data.empty:
            df_propostas_consorcio = _prepare_newcon_for_proposals(newcon_data)
            logger.info(f"NewCon processado: {len(df_propostas_consorcio)} registros")
        else:
            df_propostas_consorcio = pd.DataFrame()
            logger.warning("NewCon proposals vazio - criando DataFrame vazio")
        
        # Processar e consolidar Orbbits (todos os tipos)
        orbbits_combined = {
            'orbbits_payments': orbbits_payments,
            'orbbits_sales': orbbits_sales,
            'orbbits_proposals': orbbits_proposals
        }
        
        for orbbits_type, orbbits_data in orbbits_combined.items():
            if not orbbits_data.empty:
                df_orbbits = _prepare_orbbits_for_proposals(orbbits_data)
                
                if not df_propostas_consorcio.empty:
                    df_propostas_consorcio = pd.concat([df_propostas_consorcio, df_orbbits], ignore_index=True, sort=False)
                    logger.info(f"Consolidação com {orbbits_type}: {len(df_propostas_consorcio)} registros")
                else:
                    df_propostas_consorcio = df_orbbits
                    logger.info(f"Usando apenas {orbbits_type}: {len(df_propostas_consorcio)} registros")
        
        # Validações e limpeza
        if not df_propostas_consorcio.empty:
            df_propostas_consorcio = _safe_clean_fields(df_propostas_consorcio)
            df_propostas_consorcio = validate_required_fields(df_propostas_consorcio, 'tb_propostas')
            df_propostas_consorcio = format_data_for_salesforce(df_propostas_consorcio, 'tb_propostas')
            df_propostas_consorcio = _prepare_dataframe_for_serialization(df_propostas_consorcio)
            
            logger.info(f"✅ Propostas Consórcio transformadas: {len(df_propostas_consorcio)} registros finais")
        else:
            logger.warning("⚠️ Nenhum dado de propostas do Consórcio para processar")
        
        return {'tb_propostas': df_propostas_consorcio}
        
    except Exception as e:
        logger.error(f"❌ Erro na transformação de propostas Consórcio: {e}")
        raise

# =============================================================================
# TRANSFORMAÇÕES ESPECÍFICAS DE SEGUROS
# =============================================================================

def transform_clientes_seguros(extracted_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
    """
    Transforma dados de clientes APENAS de Seguros (Quiver).

    FONTES PROCESSADAS:
    - quiver_clients

    FONTES IGNORADAS:
    - newcon_clients, orbbits_origin (processados separadamente em Consórcio)

    Args:
        extracted_data: Dict com DataFrames extraídos

    Returns:
        Dict com DataFrame transformado: {'tb_clientes': df_clientes_seguros}
    """
    logger.info("🛡️ Iniciando transformação de clientes - SEGUROS")

    try:
        # Extrair dados apenas de Seguros
        quiver_data = extracted_data.get('quiver_clients', pd.DataFrame())

        logger.info(f"Dados Seguros - Quiver: {len(quiver_data)} registros")

        # Processar Quiver
        if not quiver_data.empty:
            df_clientes_seguros = _prepare_quiver_for_clients(quiver_data)
            logger.info(f"Quiver processado: {len(df_clientes_seguros)} registros")
        else:
            df_clientes_seguros = pd.DataFrame()
            logger.warning("Quiver clients vazio - criando DataFrame vazio")

        # Validações e limpeza
        if not df_clientes_seguros.empty:
            df_clientes_seguros = _safe_clean_fields(df_clientes_seguros)
            df_clientes_seguros = validate_required_fields(df_clientes_seguros, 'tb_clientes')
            df_clientes_seguros = format_data_for_salesforce(df_clientes_seguros, 'tb_clientes')
            df_clientes_seguros = _prepare_dataframe_for_serialization(df_clientes_seguros)

            logger.info(f"✅ Clientes Seguros transformados: {len(df_clientes_seguros)} registros finais")
        else:
            logger.warning("⚠️ Nenhum dado de clientes de Seguros para processar")

        return {'tb_clientes': df_clientes_seguros}

    except Exception as e:
        logger.error(f"❌ Erro na transformação de clientes Seguros: {e}")
        raise

def transform_leads_seguros(extracted_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
    """
    Transforma dados de leads APENAS de Seguros (Quiver).

    FONTES PROCESSADAS:
    - quiver_leads

    FONTES IGNORADAS:
    - newcon_leads, rdstation_leads, orbbits_* (processados separadamente em Consórcio)

    Args:
        extracted_data: Dict com DataFrames extraídos

    Returns:
        Dict com DataFrame transformado: {'tb_leads': df_leads_seguros}
    """
    logger.info("🛡️ Iniciando transformação de leads - SEGUROS")

    try:
        # Extrair dados apenas de Seguros
        quiver_data = extracted_data.get('quiver_leads', pd.DataFrame())

        logger.info(f"Dados Seguros - Quiver: {len(quiver_data)} registros")

        # Processar Quiver
        if not quiver_data.empty:
            df_leads_seguros = _prepare_quiver_for_leads(quiver_data)
            logger.info(f"Quiver processado: {len(df_leads_seguros)} registros")
        else:
            df_leads_seguros = pd.DataFrame()
            logger.warning("Quiver leads vazio - criando DataFrame vazio")

        # Validações e limpeza
        if not df_leads_seguros.empty:
            df_leads_seguros = _safe_clean_fields(df_leads_seguros)
            df_leads_seguros = validate_required_fields(df_leads_seguros, 'tb_leads')
            df_leads_seguros = format_data_for_salesforce(df_leads_seguros, 'tb_leads')
            df_leads_seguros = _prepare_dataframe_for_serialization(df_leads_seguros)

            logger.info(f"✅ Leads Seguros transformados: {len(df_leads_seguros)} registros finais")
        else:
            logger.warning("⚠️ Nenhum dado de leads de Seguros para processar")

        return {'tb_leads': df_leads_seguros}

    except Exception as e:
        logger.error(f"❌ Erro na transformação de leads Seguros: {e}")
        raise

def transform_produtos_seguros(extracted_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
    """
    Transforma dados de produtos APENAS de Seguros (Quiver).

    FONTES PROCESSADAS:
    - quiver_products

    FONTES IGNORADAS:
    - newcon_products, orbbits_prices (processados separadamente em Consórcio)

    Args:
        extracted_data: Dict com DataFrames extraídos

    Returns:
        Dict com DataFrame transformado: {'tb_produtos': df_produtos_seguros}
    """
    logger.info("🛡️ Iniciando transformação de produtos - SEGUROS")

    try:
        # Extrair dados apenas de Seguros
        quiver_data = extracted_data.get('quiver_products', pd.DataFrame())

        logger.info(f"Dados Seguros - Quiver: {len(quiver_data)} registros")

        # Processar Quiver
        if not quiver_data.empty:
            df_produtos_seguros = _prepare_quiver_for_products(quiver_data)
            logger.info(f"Quiver processado: {len(df_produtos_seguros)} registros")
        else:
            df_produtos_seguros = pd.DataFrame()
            logger.warning("Quiver products vazio - criando DataFrame vazio")

        # Validações e limpeza
        if not df_produtos_seguros.empty:
            df_produtos_seguros = _safe_clean_fields(df_produtos_seguros)
            df_produtos_seguros = validate_required_fields(df_produtos_seguros, 'tb_produtos')
            df_produtos_seguros = format_data_for_salesforce(df_produtos_seguros, 'tb_produtos')
            df_produtos_seguros = _prepare_dataframe_for_serialization(df_produtos_seguros)

            logger.info(f"✅ Produtos Seguros transformados: {len(df_produtos_seguros)} registros finais")
        else:
            logger.warning("⚠️ Nenhum dado de produtos de Seguros para processar")

        return {'tb_produtos': df_produtos_seguros}

    except Exception as e:
        logger.error(f"❌ Erro na transformação de produtos Seguros: {e}")
        raise

def transform_propostas_seguros(extracted_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
    """
    Transforma dados de propostas APENAS de Seguros (Quiver).

    FONTES PROCESSADAS:
    - quiver_proposals

    FONTES IGNORADAS:
    - newcon_proposals, orbbits_* (processados separadamente em Consórcio)

    Args:
        extracted_data: Dict com DataFrames extraídos

    Returns:
        Dict com DataFrame transformado: {'tb_propostas': df_propostas_seguros}
    """
    logger.info("🛡️ Iniciando transformação de propostas - SEGUROS")

    try:
        # Extrair dados apenas de Seguros
        quiver_data = extracted_data.get('quiver_proposals', pd.DataFrame())

        logger.info(f"Dados Seguros - Quiver: {len(quiver_data)} registros")

        # Processar Quiver
        if not quiver_data.empty:
            df_propostas_seguros = _prepare_quiver_for_proposals(quiver_data)
            logger.info(f"Quiver processado: {len(df_propostas_seguros)} registros")
        else:
            df_propostas_seguros = pd.DataFrame()
            logger.warning("Quiver proposals vazio - criando DataFrame vazio")

        # Validações e limpeza
        if not df_propostas_seguros.empty:
            df_propostas_seguros = _safe_clean_fields(df_propostas_seguros)
            df_propostas_seguros = validate_required_fields(df_propostas_seguros, 'tb_propostas')
            df_propostas_seguros = format_data_for_salesforce(df_propostas_seguros, 'tb_propostas')
            df_propostas_seguros = _prepare_dataframe_for_serialization(df_propostas_seguros)

            logger.info(f"✅ Propostas Seguros transformadas: {len(df_propostas_seguros)} registros finais")
        else:
            logger.warning("⚠️ Nenhum dado de propostas de Seguros para processar")

        return {'tb_propostas': df_propostas_seguros}

    except Exception as e:
        logger.error(f"❌ Erro na transformação de propostas Seguros: {e}")
        raise

# =============================================================================
# FUNÇÕES AUXILIARES PARA INTEGRAÇÃO COM AIRFLOW
# =============================================================================

def get_transformation_function(business_unit: str, table_name: str):
    """
    Retorna a função de transformação apropriada para uma unidade de negócio e tabela.

    Args:
        business_unit: 'consorcio' ou 'seguros'
        table_name: 'clientes', 'leads', 'produtos', 'propostas'

    Returns:
        Função de transformação correspondente
    """
    function_map = {
        'consorcio': {
            'clientes': transform_clientes_consorcio,
            'leads': transform_leads_consorcio,
            'produtos': transform_produtos_consorcio,
            'propostas': transform_propostas_consorcio,
        },
        'seguros': {
            'clientes': transform_clientes_seguros,
            'leads': transform_leads_seguros,
            'produtos': transform_produtos_seguros,
            'propostas': transform_propostas_seguros,
        }
    }

    if business_unit not in function_map:
        raise ValueError(f"Unidade de negócio inválida: {business_unit}")

    if table_name not in function_map[business_unit]:
        raise ValueError(f"Tabela inválida para {business_unit}: {table_name}")

    return function_map[business_unit][table_name]

# =============================================================================
# FUNÇÃO AUXILIAR PARA RECUPERAR DADOS DO XCOM
# =============================================================================

def get_extracted_data_from_xcom_local(context) -> Dict[str, pd.DataFrame]:
    """
    Recupera dados extraídos do XCom para uso nas transformações.

    Versão local que evita dependências problemáticas do etl_main.py.

    Args:
        context: Contexto do Airflow com task instance

    Returns:
        Dict com DataFrames extraídos por fonte de dados

    Raises:
        ValueError: Se dados consolidados não forem encontrados
    """
    if 'ti' not in context:
        raise ValueError("Task instance não encontrada no contexto")

    ti = context['ti']

    # Tenta recuperar dados consolidados da extração por tabela (método preferido)
    consolidated_data = ti.xcom_pull(key='consolidated_table_extraction_result')

    if consolidated_data and consolidated_data.get('success', False):
        logger.info(f"✅ Dados consolidados recuperados: {consolidated_data.get('total_records', 0)} registros")
        return consolidated_data.get('data', {})

    # Fallback: tenta recuperar dados consolidados da extração por fonte
    consolidated_data = ti.xcom_pull(key='consolidated_extraction_result')

    if consolidated_data and consolidated_data.get('success', False):
        logger.info(f"✅ Dados consolidados (fallback) recuperados: {consolidated_data.get('total_records', 0)} registros")
        return consolidated_data.get('data', {})

    # Último fallback: tenta recuperar dados da extração geral
    extraction_data = ti.xcom_pull(key='extraction_result')

    if extraction_data and extraction_data.get('success', False):
        logger.info(f"✅ Dados de extração geral recuperados: {extraction_data.get('total_records', 0)} registros")
        return extraction_data.get('data', {})

    # Se nenhum dado foi encontrado, retorna dict vazio
    logger.warning("⚠️ Nenhum dado consolidado encontrado no XCom - retornando dict vazio")
    return {}

# =============================================================================
# FUNÇÕES WRAPPER PARA AIRFLOW (COMPATIBILIDADE)
# =============================================================================

def airflow_transform_clientes_consorcio_task(**context):
    """Wrapper Airflow para transformação de clientes do Consórcio"""
    try:
        extracted_data = get_extracted_data_from_xcom_local(context)
        result = transform_clientes_consorcio(extracted_data)
        logger.info(f"🏢 Clientes Consórcio: {len(result['tb_clientes'])} registros transformados")
        return result
    except Exception as e:
        logger.error(f"❌ Erro em transform_clientes_consorcio: {e}")
        raise

def airflow_transform_leads_consorcio_task(**context):
    """Wrapper Airflow para transformação de leads do Consórcio"""
    try:
        extracted_data = get_extracted_data_from_xcom_local(context)
        result = transform_leads_consorcio(extracted_data)
        logger.info(f"🏢 Leads Consórcio: {len(result['tb_leads'])} registros transformados")
        return result
    except Exception as e:
        logger.error(f"❌ Erro em transform_leads_consorcio: {e}")
        raise

def airflow_transform_produtos_consorcio_task(**context):
    """Wrapper Airflow para transformação de produtos do Consórcio"""
    try:
        extracted_data = get_extracted_data_from_xcom_local(context)
        result = transform_produtos_consorcio(extracted_data)
        logger.info(f"🏢 Produtos Consórcio: {len(result['tb_produtos'])} registros transformados")
        return result
    except Exception as e:
        logger.error(f"❌ Erro em transform_produtos_consorcio: {e}")
        raise

def airflow_transform_propostas_consorcio_task(**context):
    """Wrapper Airflow para transformação de propostas do Consórcio"""
    try:
        extracted_data = get_extracted_data_from_xcom_local(context)
        result = transform_propostas_consorcio(extracted_data)
        logger.info(f"🏢 Propostas Consórcio: {len(result['tb_propostas'])} registros transformados")
        return result
    except Exception as e:
        logger.error(f"❌ Erro em transform_propostas_consorcio: {e}")
        raise

def airflow_transform_clientes_seguros_task(**context):
    """Wrapper Airflow para transformação de clientes de Seguros"""
    try:
        extracted_data = get_extracted_data_from_xcom_local(context)
        result = transform_clientes_seguros(extracted_data)
        logger.info(f"🛡️ Clientes Seguros: {len(result['tb_clientes'])} registros transformados")
        return result
    except Exception as e:
        logger.error(f"❌ Erro em transform_clientes_seguros: {e}")
        raise

def airflow_transform_leads_seguros_task(**context):
    """Wrapper Airflow para transformação de leads de Seguros"""
    try:
        extracted_data = get_extracted_data_from_xcom_local(context)
        result = transform_leads_seguros(extracted_data)
        logger.info(f"🛡️ Leads Seguros: {len(result['tb_leads'])} registros transformados")
        return result
    except Exception as e:
        logger.error(f"❌ Erro em transform_leads_seguros: {e}")
        raise

def airflow_transform_produtos_seguros_task(**context):
    """Wrapper Airflow para transformação de produtos de Seguros"""
    try:
        extracted_data = get_extracted_data_from_xcom_local(context)
        result = transform_produtos_seguros(extracted_data)
        logger.info(f"🛡️ Produtos Seguros: {len(result['tb_produtos'])} registros transformados")
        return result
    except Exception as e:
        logger.error(f"❌ Erro em transform_produtos_seguros: {e}")
        raise

def airflow_transform_propostas_seguros_task(**context):
    """Wrapper Airflow para transformação de propostas de Seguros"""
    try:
        extracted_data = get_extracted_data_from_xcom_local(context)
        result = transform_propostas_seguros(extracted_data)
        logger.info(f"🛡️ Propostas Seguros: {len(result['tb_propostas'])} registros transformados")
        return result
    except Exception as e:
        logger.error(f"❌ Erro em transform_propostas_seguros: {e}")
        raise

# =============================================================================
# FUNÇÃO DE TESTE
# =============================================================================

def test_business_unit_transformations():
    """Testa as transformações por unidade de negócio"""
    print("🧪 Testando transformações por unidade de negócio...")

    # Dados de teste simulados
    test_data = {
        'newcon_clients': pd.DataFrame({'id': [1, 2], 'name': ['Cliente 1', 'Cliente 2']}),
        'quiver_clients': pd.DataFrame({'id': [3, 4], 'name': ['Cliente 3', 'Cliente 4']}),
        'newcon_leads': pd.DataFrame({'id': [1, 2], 'lead': ['Lead 1', 'Lead 2']}),
        'quiver_leads': pd.DataFrame({'id': [3, 4], 'lead': ['Lead 3', 'Lead 4']}),
    }

    try:
        # Teste Consórcio
        result_consorcio = transform_clientes_consorcio(test_data)
        print(f"✅ Clientes Consórcio: {len(result_consorcio['tb_clientes'])} registros")

        # Teste Seguros
        result_seguros = transform_clientes_seguros(test_data)
        print(f"✅ Clientes Seguros: {len(result_seguros['tb_clientes'])} registros")

        print("🎉 Testes das transformações passaram!")
        return True

    except Exception as e:
        print(f"❌ Erro nos testes: {e}")
        return False

if __name__ == "__main__":
    test_business_unit_transformations()
