"""
DAG Builder - Factory para Construção de DAGs por Unidade de Negócio

Implementa o padrão Factory para criar DAGs específicas para cada unidade de negócio,
mantendo a lógica comum na classe base e permitindo customizações específicas.
"""

import os
import sys
import logging
from typing import Dict, List, Any, Optional
from datetime import timedelta
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator

# Adicionar o diretório atual ao path para importações
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, current_dir)
sys.path.insert(0, parent_dir)

from dag_factory.base_dag import BaseSalesforceDAG

# Importar funções do ETL existente
try:
    from etl_main import (
        # Funções de extração - Consórcio
        airflow_extract_newcon_clients_individual_task,
        airflow_extract_newcon_products_individual_task,
        airflow_extract_newcon_leads_individual_task,
        airflow_extract_newcon_proposals_individual_task,
        airflow_extract_rdstation_leads_individual_task,
        airflow_extract_orbbits_origin_individual_task,
        airflow_extract_orbbits_payments_individual_task,
        airflow_extract_orbbits_sales_individual_task,
        airflow_extract_orbbits_prices_individual_task,
        airflow_extract_orbbits_proposals_individual_task,
        
        # Funções de extração - Seguros
        airflow_extract_quiver_clients_individual_task,
        airflow_extract_quiver_leads_individual_task,
        airflow_extract_quiver_products_individual_task,
        airflow_extract_quiver_proposals_individual_task,
        
        # Funções de consolidação
        airflow_consolidate_table_extractions_task,
        
        # Funções de transformação
        airflow_transform_produtos_task,
        airflow_transform_clientes_task,
        airflow_transform_leads_task,
        airflow_transform_propostas_task,
        
        # Funções de carregamento
        airflow_load_produtos_parallel_task,
        airflow_load_clientes_parallel_task,
        airflow_load_leads_parallel_task,
        airflow_load_propostas_parallel_task,
        
        # Funções de exportação CSV
        airflow_export_produtos_csv_task,
        airflow_export_clientes_csv_task,
        airflow_export_leads_csv_task,
        airflow_export_propostas_csv_task,
        
        # Função de relatório
        airflow_generate_failure_report_task
    )
except ImportError as e:
    logging.error(f"Erro ao importar funções do ETL: {e}")
    # Funções de fallback
    def fallback_function(**context):
        logging.error("Função não disponível - erro de importação")
        raise ImportError("etl_main não pôde ser importado")
    
    # Atribuir fallback para todas as funções
    globals().update({name: fallback_function for name in [
        'airflow_extract_newcon_clients_individual_task',
        'airflow_extract_newcon_products_individual_task',
        'airflow_extract_newcon_leads_individual_task',
        'airflow_extract_newcon_proposals_individual_task',
        'airflow_extract_rdstation_leads_individual_task',
        'airflow_extract_orbbits_origin_individual_task',
        'airflow_extract_orbbits_payments_individual_task',
        'airflow_extract_orbbits_sales_individual_task',
        'airflow_extract_orbbits_prices_individual_task',
        'airflow_extract_orbbits_proposals_individual_task',
        'airflow_extract_quiver_clients_individual_task',
        'airflow_extract_quiver_leads_individual_task',
        'airflow_extract_quiver_products_individual_task',
        'airflow_extract_quiver_proposals_individual_task',
        'airflow_consolidate_table_extractions_task',
        'airflow_transform_produtos_task',
        'airflow_transform_clientes_task',
        'airflow_transform_leads_task',
        'airflow_transform_propostas_task',
        'airflow_load_produtos_parallel_task',
        'airflow_load_clientes_parallel_task',
        'airflow_load_leads_parallel_task',
        'airflow_load_propostas_parallel_task',
        'airflow_export_produtos_csv_task',
        'airflow_export_clientes_csv_task',
        'airflow_export_leads_csv_task',
        'airflow_export_propostas_csv_task',
        'airflow_generate_failure_report_task'
    ]})

class ConsorcioSalesforceDAG(BaseSalesforceDAG):
    """DAG específica para a unidade de negócio Consórcio"""
    
    def get_business_unit_name(self) -> str:
        return "consorcio"
    
    def get_custom_dag_args(self) -> Dict[str, Any]:
        return {
            'email': ['<EMAIL>'],
            'sla': timedelta(hours=2),
        }
    
    def _create_extraction_tasks(self) -> List[PythonOperator]:
        """Cria tarefas de extração para Consórcio"""
        extraction_tasks = []
        
        # Mapeamento de tarefas de extração do Consórcio
        extraction_configs = [
            ('extract_newcon_clients', airflow_extract_newcon_clients_individual_task, 15, 10),
            ('extract_newcon_products', airflow_extract_newcon_products_individual_task, 10, 9),
            ('extract_newcon_leads', airflow_extract_newcon_leads_individual_task, 12, 8),
            ('extract_newcon_proposals', airflow_extract_newcon_proposals_individual_task, 25, 7),
            ('extract_rdstation_leads', airflow_extract_rdstation_leads_individual_task, 15, 6),
            ('extract_orbbits_origin', airflow_extract_orbbits_origin_individual_task, 8, 5),
            ('extract_orbbits_payments', airflow_extract_orbbits_payments_individual_task, 8, 4),
            ('extract_orbbits_sales', airflow_extract_orbbits_sales_individual_task, 8, 3),
            ('extract_orbbits_prices', airflow_extract_orbbits_prices_individual_task, 8, 2),
            ('extract_orbbits_proposals', airflow_extract_orbbits_proposals_individual_task, 8, 1),
        ]
        
        for task_id, callable_func, timeout_min, priority in extraction_configs:
            task = PythonOperator(
                task_id=task_id,
                python_callable=callable_func,
                dag=self.dag,
                doc_md=f"Extração {task_id.replace('extract_', '').replace('_', ' ').title()} - Consórcio",
                priority_weight=priority,
                execution_timeout=timedelta(minutes=timeout_min),
            )
            extraction_tasks.append(task)
        
        return extraction_tasks
    
    def _create_consolidation_task(self) -> PythonOperator:
        """Cria tarefa de consolidação para Consórcio"""
        return PythonOperator(
            task_id='consolidate_consorcio_extractions',
            python_callable=airflow_consolidate_table_extractions_task,
            dag=self.dag,
            doc_md="Consolida extrações do Consórcio (NewCon, RD Station, Orbbits)",
            priority_weight=11,
            execution_timeout=timedelta(minutes=5),
        )
    
    def _create_transformation_tasks(self) -> List[PythonOperator]:
        """Cria tarefas de transformação para Consórcio"""
        return [
            PythonOperator(
                task_id='transform_produtos_consorcio',
                python_callable=airflow_transform_produtos_task,
                dag=self.dag,
                doc_md="Transformação Produtos - Consórcio",
                priority_weight=10,
                execution_timeout=timedelta(minutes=8),
            ),
            PythonOperator(
                task_id='transform_clientes_consorcio',
                python_callable=airflow_transform_clientes_task,
                dag=self.dag,
                doc_md="Transformação Clientes - Consórcio",
                priority_weight=9,
                execution_timeout=timedelta(minutes=12),
            ),
            PythonOperator(
                task_id='transform_leads_consorcio',
                python_callable=airflow_transform_leads_task,
                dag=self.dag,
                doc_md="Transformação Leads - Consórcio",
                priority_weight=8,
                execution_timeout=timedelta(minutes=10),
            ),
            PythonOperator(
                task_id='transform_propostas_consorcio',
                python_callable=airflow_transform_propostas_task,
                dag=self.dag,
                doc_md="Transformação Propostas - Consórcio",
                priority_weight=7,
                execution_timeout=timedelta(minutes=15),
            ),
        ]
    
    def _create_load_tasks(self) -> List[PythonOperator]:
        """Cria tarefas de carregamento para Consórcio"""
        load_configs = [
            ('produtos', airflow_load_produtos_parallel_task, airflow_export_produtos_csv_task, 8, 10),
            ('clientes', airflow_load_clientes_parallel_task, airflow_export_clientes_csv_task, 12, 9),
            ('leads', airflow_load_leads_parallel_task, airflow_export_leads_csv_task, 45, 8),
            ('propostas', airflow_load_propostas_parallel_task, airflow_export_propostas_csv_task, 25, 7),
        ]
        
        load_tasks = []
        for table, load_func, export_func, timeout_min, priority in load_configs:
            output_func = export_func if self.is_csv_mode() else load_func
            task_id = f'export_{table}_csv_consorcio' if self.is_csv_mode() else f'load_{table}_consorcio'
            
            task = PythonOperator(
                task_id=task_id,
                python_callable=output_func,
                dag=self.dag,
                doc_md=f"{'Exportação CSV' if self.is_csv_mode() else 'Carregamento'} {table.title()} - Consórcio",
                priority_weight=priority,
                execution_timeout=timedelta(minutes=timeout_min),
            )
            load_tasks.append(task)
        
        return load_tasks
    
    def _set_task_dependencies(self) -> None:
        """Define dependências para Consórcio"""
        # Início → Extrações
        self.tasks['start'] >> self.tasks['extractions']
        
        # Extrações → Consolidação
        self.tasks['extractions'] >> self.tasks['consolidation']
        
        # Consolidação → Transformações
        self.tasks['consolidation'] >> self.tasks['transformations']
        
        # Transformações → Carregamentos (1:1)
        for transform_task, load_task in zip(self.tasks['transformations'], self.tasks['loads']):
            transform_task >> load_task
        
        # Carregamentos → Relatório → Fim
        self.tasks['loads'] >> self.tasks['report'] >> self.tasks['end']
    
    def _get_report_function(self):
        return airflow_generate_failure_report_task

class SegurosSalesforceDAG(BaseSalesforceDAG):
    """DAG específica para a unidade de negócio Seguros"""
    
    def get_business_unit_name(self) -> str:
        return "seguros"
    
    def get_custom_dag_args(self) -> Dict[str, Any]:
        return {
            'email': ['<EMAIL>'],
            'sla': timedelta(hours=1),  # SLA menor pois tem menos dados
        }
    
    def _create_extraction_tasks(self) -> List[PythonOperator]:
        """Cria tarefas de extração para Seguros"""
        extraction_tasks = []
        
        # Mapeamento de tarefas de extração do Seguros (apenas Quiver)
        extraction_configs = [
            ('extract_quiver_clients', airflow_extract_quiver_clients_individual_task, 10, 8),
            ('extract_quiver_leads', airflow_extract_quiver_leads_individual_task, 8, 7),
            ('extract_quiver_products', airflow_extract_quiver_products_individual_task, 6, 6),
            ('extract_quiver_proposals', airflow_extract_quiver_proposals_individual_task, 12, 9),
        ]
        
        for task_id, callable_func, timeout_min, priority in extraction_configs:
            task = PythonOperator(
                task_id=task_id,
                python_callable=callable_func,
                dag=self.dag,
                doc_md=f"Extração {task_id.replace('extract_quiver_', '').title()} - Seguros",
                priority_weight=priority,
                execution_timeout=timedelta(minutes=timeout_min),
            )
            extraction_tasks.append(task)
        
        return extraction_tasks
    
    def _create_consolidation_task(self) -> PythonOperator:
        """Cria tarefa de consolidação para Seguros"""
        return PythonOperator(
            task_id='consolidate_seguros_extractions',
            python_callable=airflow_consolidate_table_extractions_task,
            dag=self.dag,
            doc_md="Consolida extrações dos Seguros (Quiver)",
            priority_weight=11,
            execution_timeout=timedelta(minutes=3),  # Menor timeout
        )
    
    def _create_transformation_tasks(self) -> List[PythonOperator]:
        """Cria tarefas de transformação para Seguros"""
        return [
            PythonOperator(
                task_id='transform_produtos_seguros',
                python_callable=airflow_transform_produtos_task,
                dag=self.dag,
                doc_md="Transformação Produtos - Seguros",
                priority_weight=10,
                execution_timeout=timedelta(minutes=5),
            ),
            PythonOperator(
                task_id='transform_clientes_seguros',
                python_callable=airflow_transform_clientes_task,
                dag=self.dag,
                doc_md="Transformação Clientes - Seguros",
                priority_weight=9,
                execution_timeout=timedelta(minutes=8),
            ),
            PythonOperator(
                task_id='transform_leads_seguros',
                python_callable=airflow_transform_leads_task,
                dag=self.dag,
                doc_md="Transformação Leads - Seguros",
                priority_weight=8,
                execution_timeout=timedelta(minutes=6),
            ),
            PythonOperator(
                task_id='transform_propostas_seguros',
                python_callable=airflow_transform_propostas_task,
                dag=self.dag,
                doc_md="Transformação Propostas - Seguros",
                priority_weight=7,
                execution_timeout=timedelta(minutes=10),
            ),
        ]
    
    def _create_load_tasks(self) -> List[PythonOperator]:
        """Cria tarefas de carregamento para Seguros"""
        load_configs = [
            ('produtos', airflow_load_produtos_parallel_task, airflow_export_produtos_csv_task, 5, 10),
            ('clientes', airflow_load_clientes_parallel_task, airflow_export_clientes_csv_task, 8, 9),
            ('leads', airflow_load_leads_parallel_task, airflow_export_leads_csv_task, 15, 8),
            ('propostas', airflow_load_propostas_parallel_task, airflow_export_propostas_csv_task, 15, 7),
        ]
        
        load_tasks = []
        for table, load_func, export_func, timeout_min, priority in load_configs:
            output_func = export_func if self.is_csv_mode() else load_func
            task_id = f'export_{table}_csv_seguros' if self.is_csv_mode() else f'load_{table}_seguros'
            
            task = PythonOperator(
                task_id=task_id,
                python_callable=output_func,
                dag=self.dag,
                doc_md=f"{'Exportação CSV' if self.is_csv_mode() else 'Carregamento'} {table.title()} - Seguros",
                priority_weight=priority,
                execution_timeout=timedelta(minutes=timeout_min),
            )
            load_tasks.append(task)
        
        return load_tasks
    
    def _set_task_dependencies(self) -> None:
        """Define dependências para Seguros"""
        # Início → Extrações
        self.tasks['start'] >> self.tasks['extractions']
        
        # Extrações → Consolidação
        self.tasks['extractions'] >> self.tasks['consolidation']
        
        # Consolidação → Transformações
        self.tasks['consolidation'] >> self.tasks['transformations']
        
        # Transformações → Carregamentos (1:1)
        for transform_task, load_task in zip(self.tasks['transformations'], self.tasks['loads']):
            transform_task >> load_task
        
        # Carregamentos → Relatório → Fim
        self.tasks['loads'] >> self.tasks['report'] >> self.tasks['end']
    
    def _get_report_function(self):
        return airflow_generate_failure_report_task

# =============================================================================
# FACTORY FUNCTIONS
# =============================================================================

def create_consorcio_dag(output_mode: str = 'salesforce') -> 'DAG':
    """Cria DAG para Consórcio"""
    dag_builder = ConsorcioSalesforceDAG('consorcio', output_mode)
    dag_builder.create_tasks()
    return dag_builder.get_dag()

def create_seguros_dag(output_mode: str = 'salesforce') -> 'DAG':
    """Cria DAG para Seguros"""
    dag_builder = SegurosSalesforceDAG('seguros', output_mode)
    dag_builder.create_tasks()
    return dag_builder.get_dag()
