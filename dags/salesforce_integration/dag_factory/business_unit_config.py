"""
Configurações por Unidade de Negócio

Define as configurações específicas para cada unidade de negócio:
- Consórcio: NewCon, RD Station, Orbbits
- Seguros: Quiver

Cada unidade tem suas próprias fontes de dados, Data Extensions e configurações.
"""

from typing import Dict, List, Any
from datetime import timedelta

# =============================================================================
# MAPEAMENTO DE FONTES DE DADOS POR UNIDADE DE NEGÓCIO
# =============================================================================

BUSINESS_UNIT_DATA_SOURCES = {
    'consorcio': {
        'databases': ['newcon', 'rdstation', 'orbbits'],
        'extraction_tasks': [
            'extract_newcon_clients',
            'extract_newcon_products', 
            'extract_newcon_leads',
            'extract_newcon_proposals',
            'extract_rdstation_leads',
            'extract_orbbits_origin',
            'extract_orbbits_payments',
            'extract_orbbits_sales',
            'extract_orbbits_prices',
            'extract_orbbits_proposals'
        ]
    },
    'seguros': {
        'databases': ['quiver'],
        'extraction_tasks': [
            'extract_quiver_clients',
            'extract_quiver_leads',
            'extract_quiver_products',
            'extract_quiver_proposals'
        ]
    }
}

# =============================================================================
# DATA EXTENSIONS POR UNIDADE DE NEGÓCIO
# =============================================================================

BUSINESS_UNIT_DATA_EXTENSIONS = {
    'consorcio': {
        'tb_clientes': {
            'external_key': '6E536388-F5A4-416D-B0DE-AABE229F33C1',  # Reutiliza a existente
            'suffix': '_consorcio',
            'required_fields': ['cnpjcpf', 'email'],
            'estimated_records': 73498,
            'batch_count': 37,
            'priority': 2,
        },
        'tb_leads': {
            'external_key': '7C8AEDB6-64E8-41FC-965F-A4C37DF0ABE0',  # Reutiliza a existente
            'suffix': '_consorcio',
            'required_fields': ['cnpjcpf', 'dt_simulacao'],
            'estimated_records': 0,
            'batch_count': 0,
            'priority': 3,
        },
        'tb_produtos': {
            'external_key': '19BEF707-C44F-4C3A-A9FB-53D2915258F1',  # Reutiliza a existente
            'suffix': '_consorcio',
            'required_fields': ['id_produto'],
            'estimated_records': 20502,
            'batch_count': 11,
            'priority': 1,
        },
        'tb_propostas': {
            'external_key': '9FC20C5F-04EC-4B86-9491-D649ECFACAA5',  # Reutiliza a existente
            'suffix': '_consorcio',
            'required_fields': ['idproposta', 'email'],
            'estimated_records': 533238,
            'batch_count': 267,
            'priority': 4,
        }
    },
    'seguros': {
        'tb_clientes': {
            'external_key': 'NEW_UUID_CLIENTES_SEGUROS',  # Precisa criar nova Data Extension
            'suffix': '_seguros',
            'required_fields': ['cnpjcpf', 'email'],
            'estimated_records': 20000,  # Estimativa baseada no Quiver
            'batch_count': 10,
            'priority': 2,
        },
        'tb_leads': {
            'external_key': 'NEW_UUID_LEADS_SEGUROS',  # Precisa criar nova Data Extension
            'suffix': '_seguros',
            'required_fields': ['cnpjcpf', 'dt_simulacao'],
            'estimated_records': 0,
            'batch_count': 0,
            'priority': 3,
        },
        'tb_produtos': {
            'external_key': 'NEW_UUID_PRODUTOS_SEGUROS',  # Precisa criar nova Data Extension
            'suffix': '_seguros',
            'required_fields': ['id_produto'],
            'estimated_records': 5000,  # Estimativa baseada no Quiver
            'batch_count': 3,
            'priority': 1,
        },
        'tb_propostas': {
            'external_key': 'NEW_UUID_PROPOSTAS_SEGUROS',  # Precisa criar nova Data Extension
            'suffix': '_seguros',
            'required_fields': ['idproposta', 'email'],
            'estimated_records': 100000,  # Estimativa baseada no Quiver
            'batch_count': 50,
            'priority': 4,
        }
    }
}

# =============================================================================
# CONFIGURAÇÕES DE DAG POR UNIDADE DE NEGÓCIO
# =============================================================================

BUSINESS_UNIT_DAG_CONFIGS = {
    'consorcio': {
        'dag_id': 'INTEGRACAO-SALESFORCE-CONSORCIO',
        'description': 'ETL Pipeline Consórcio - NewCon, RD Station, Orbbits → Salesforce',
        'schedule_interval': '0 8 * * *',  # 8h da manhã
        'max_active_runs': 1,
        'catchup': False,
        'tags': ['salesforce', 'consorcio', 'newcon', 'rdstation', 'orbbits'],
        'owner': 'etl_team_consorcio',
        'retries': 2,
        'retry_delay': timedelta(minutes=3),
        'execution_timeout': timedelta(hours=2),
    },
    'seguros': {
        'dag_id': 'INTEGRACAO-SALESFORCE-SEGUROS',
        'description': 'ETL Pipeline Seguros - Quiver → Salesforce',
        'schedule_interval': '0 9 * * *',  # 9h da manhã (1h após consórcio)
        'max_active_runs': 1,
        'catchup': False,
        'tags': ['salesforce', 'seguros', 'quiver'],
        'owner': 'etl_team_seguros',
        'retries': 2,
        'retry_delay': timedelta(minutes=3),
        'execution_timeout': timedelta(hours=1),  # Menor timeout pois tem menos dados
    }
}

# =============================================================================
# FUNÇÕES AUXILIARES
# =============================================================================

def get_business_unit_data_sources(business_unit: str) -> Dict[str, Any]:
    """Retorna as fontes de dados para uma unidade de negócio"""
    return BUSINESS_UNIT_DATA_SOURCES.get(business_unit, {})

def get_business_unit_data_extensions(business_unit: str) -> Dict[str, Any]:
    """Retorna as Data Extensions para uma unidade de negócio"""
    return BUSINESS_UNIT_DATA_EXTENSIONS.get(business_unit, {})

def get_business_unit_dag_config(business_unit: str) -> Dict[str, Any]:
    """Retorna a configuração da DAG para uma unidade de negócio"""
    return BUSINESS_UNIT_DAG_CONFIGS.get(business_unit, {})

def get_available_business_units() -> List[str]:
    """Retorna lista de unidades de negócio disponíveis"""
    return list(BUSINESS_UNIT_DAG_CONFIGS.keys())

def validate_business_unit(business_unit: str) -> bool:
    """Valida se a unidade de negócio existe"""
    return business_unit in get_available_business_units()

# =============================================================================
# MAPEAMENTO DE FUNÇÕES DE EXTRAÇÃO POR UNIDADE DE NEGÓCIO
# =============================================================================

BUSINESS_UNIT_EXTRACTION_FUNCTIONS = {
    'consorcio': {
        'extract_newcon_clients': 'airflow_extract_newcon_clients_individual_task',
        'extract_newcon_products': 'airflow_extract_newcon_products_individual_task',
        'extract_newcon_leads': 'airflow_extract_newcon_leads_individual_task',
        'extract_newcon_proposals': 'airflow_extract_newcon_proposals_individual_task',
        'extract_rdstation_leads': 'airflow_extract_rdstation_leads_individual_task',
        'extract_orbbits_origin': 'airflow_extract_orbbits_origin_individual_task',
        'extract_orbbits_payments': 'airflow_extract_orbbits_payments_individual_task',
        'extract_orbbits_sales': 'airflow_extract_orbbits_sales_individual_task',
        'extract_orbbits_prices': 'airflow_extract_orbbits_prices_individual_task',
        'extract_orbbits_proposals': 'airflow_extract_orbbits_proposals_individual_task'
    },
    'seguros': {
        'extract_quiver_clients': 'airflow_extract_quiver_clients_individual_task',
        'extract_quiver_leads': 'airflow_extract_quiver_leads_individual_task',
        'extract_quiver_products': 'airflow_extract_quiver_products_individual_task',
        'extract_quiver_proposals': 'airflow_extract_quiver_proposals_individual_task'
    }
}

def get_extraction_functions(business_unit: str) -> Dict[str, str]:
    """Retorna as funções de extração para uma unidade de negócio"""
    return BUSINESS_UNIT_EXTRACTION_FUNCTIONS.get(business_unit, {})
