"""
Classe Base Abstrata para DAGs do Salesforce

Define a estrutura comum para todas as DAGs do Salesforce por unidade de negócio.
Implementa o padrão Template Method para reutilização máxima de código.
"""

from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
import logging
import os
import sys

# Adicionar o diretório atual ao path para importações
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, current_dir)
sys.path.insert(0, parent_dir)

from dag_factory.business_unit_config import (
    get_business_unit_data_sources,
    get_business_unit_data_extensions,
    get_business_unit_dag_config,
    get_extraction_functions,
    validate_business_unit
)

class BaseSalesforceDAG(ABC):
    """
    Classe base abstrata para DAGs do Salesforce por unidade de negócio.
    
    Implementa o padrão Template Method onde:
    - A estrutura geral da DAG é definida na classe base
    - Detalhes específicos são implementados pelas classes filhas
    - Máxima reutilização de código com flexibilidade para customização
    """
    
    def __init__(self, business_unit: str, output_mode: str = 'salesforce'):
        """
        Inicializa a DAG base.
        
        Args:
            business_unit: Nome da unidade de negócio ('consorcio' ou 'seguros')
            output_mode: Modo de saída ('salesforce' ou 'csv')
        """
        if not validate_business_unit(business_unit):
            raise ValueError(f"Unidade de negócio inválida: {business_unit}")
            
        self.business_unit = business_unit
        self.output_mode = output_mode
        self.logger = logging.getLogger(f"{__name__}.{business_unit}")
        
        # Carrega configurações específicas da unidade de negócio
        self.data_sources = get_business_unit_data_sources(business_unit)
        self.data_extensions = get_business_unit_data_extensions(business_unit)
        self.dag_config = get_business_unit_dag_config(business_unit)
        self.extraction_functions = get_extraction_functions(business_unit)
        
        # Inicializa a DAG
        self.dag = self._create_dag()
        self.tasks = {}
        
        self.logger.info(f"DAG {business_unit} inicializada com {len(self.data_sources.get('extraction_tasks', []))} tarefas de extração")
    
    @abstractmethod
    def get_business_unit_name(self) -> str:
        """Retorna o nome da unidade de negócio"""
        pass
    
    @abstractmethod
    def get_custom_dag_args(self) -> Dict[str, Any]:
        """Retorna argumentos customizados específicos da unidade de negócio"""
        pass
    
    def _create_dag(self) -> DAG:
        """Cria a instância da DAG com configurações da unidade de negócio"""
        default_args = {
            'owner': self.dag_config.get('owner', 'etl_team'),
            'depends_on_past': False,
            'start_date': datetime(2025, 1, 1),
            'email_on_failure': False,
            'email_on_retry': False,
            'retries': self.dag_config.get('retries', 2),
            'retry_delay': self.dag_config.get('retry_delay', timedelta(minutes=3)),
            'execution_timeout': self.dag_config.get('execution_timeout', timedelta(hours=2)),
            'on_failure_callback': None,
            'on_success_callback': None,
            'on_retry_callback': None,
        }
        
        # Merge com argumentos customizados
        custom_args = self.get_custom_dag_args()
        default_args.update(custom_args)
        
        dag = DAG(
            dag_id=self.dag_config.get('dag_id', f'INTEGRACAO-SALESFORCE-{self.business_unit.upper()}'),
            default_args=default_args,
            description=self.dag_config.get('description', f'ETL Pipeline {self.business_unit.title()}'),
            schedule_interval=self.dag_config.get('schedule_interval', '0 8 * * *'),
            catchup=self.dag_config.get('catchup', False),
            max_active_runs=self.dag_config.get('max_active_runs', 1),
            tags=self.dag_config.get('tags', ['salesforce', self.business_unit]),
            doc_md=self._generate_dag_documentation(),
        )
        
        return dag
    
    def _generate_dag_documentation(self) -> str:
        """Gera documentação automática da DAG"""
        extraction_count = len(self.data_sources.get('extraction_tasks', []))
        databases = ', '.join(self.data_sources.get('databases', []))
        
        return f"""
        # ETL Salesforce Marketing Cloud - {self.business_unit.title()}

        Pipeline ETL especializado para a unidade de negócio **{self.business_unit.title()}**.

        ## ⚙️ Configuração Atual
        
        **Modo de saída: {self.output_mode.upper()}**
        **Unidade de negócio: {self.business_unit.title()}**
        **Fontes de dados: {databases}**
        **Tarefas de extração: {extraction_count}**
        
        ## Arquitetura da Pipeline:

        ### Fase 1: Extração Paralela ({extraction_count} Tarefas)
        {self._generate_extraction_docs()}
        
        ### Fase 2: Consolidação
        - Unifica dados de todas as extrações da unidade
        
        ### Fase 3: Transformação Paralela (4 Tabelas)
        - Produtos, Clientes, Leads, Propostas
        
        ### Fase 4: Carregamento/Exportação
        - Modo Salesforce: Carrega nas Data Extensions específicas
        - Modo CSV: Exporta arquivos para revisão
        
        ## Data Extensions de Destino:
        {self._generate_data_extensions_docs()}
        """
    
    def _generate_extraction_docs(self) -> str:
        """Gera documentação das tarefas de extração"""
        docs = []
        for task in self.data_sources.get('extraction_tasks', []):
            docs.append(f"- **{task}**: Extração independente com isolamento de falhas")
        return '\n        '.join(docs)
    
    def _generate_data_extensions_docs(self) -> str:
        """Gera documentação das Data Extensions"""
        docs = []
        for table, config in self.data_extensions.items():
            suffix = config.get('suffix', '')
            records = config.get('estimated_records', 0)
            docs.append(f"- **{table}{suffix}**: ~{records:,} registros estimados")
        return '\n        '.join(docs)
    
    def create_tasks(self) -> Dict[str, Any]:
        """
        Cria todas as tarefas da DAG.
        Template Method - estrutura comum, implementação específica nas filhas.
        """
        # Tarefas de controle
        self.tasks['start'] = self._create_start_task()
        self.tasks['end'] = self._create_end_task()
        self.tasks['report'] = self._create_report_task()
        
        # Tarefas de extração (específicas por unidade de negócio)
        self.tasks['extractions'] = self._create_extraction_tasks()
        
        # Tarefa de consolidação
        self.tasks['consolidation'] = self._create_consolidation_task()
        
        # Tarefas de transformação
        self.tasks['transformations'] = self._create_transformation_tasks()
        
        # Tarefas de carregamento/exportação
        self.tasks['loads'] = self._create_load_tasks()
        
        # Define dependências
        self._set_task_dependencies()
        
        return self.tasks
    
    def _create_start_task(self) -> DummyOperator:
        """Cria tarefa de início"""
        return DummyOperator(
            task_id='start_pipeline',
            dag=self.dag,
            doc_md=f"Início do pipeline ETL {self.business_unit}"
        )
    
    def _create_end_task(self) -> DummyOperator:
        """Cria tarefa de fim"""
        return DummyOperator(
            task_id='end_pipeline',
            dag=self.dag,
            doc_md=f"Fim do pipeline ETL {self.business_unit}"
        )
    
    def _create_report_task(self) -> PythonOperator:
        """Cria tarefa de relatório"""
        return PythonOperator(
            task_id='generate_failure_report',
            python_callable=self._get_report_function(),
            dag=self.dag,
            doc_md=f"Gera relatório de falhas do pipeline {self.business_unit}",
            trigger_rule='none_failed_or_skipped'
        )
    
    @abstractmethod
    def _create_extraction_tasks(self) -> List[PythonOperator]:
        """Cria tarefas de extração específicas da unidade de negócio"""
        pass
    
    @abstractmethod
    def _create_consolidation_task(self) -> PythonOperator:
        """Cria tarefa de consolidação específica da unidade de negócio"""
        pass
    
    @abstractmethod
    def _create_transformation_tasks(self) -> List[PythonOperator]:
        """Cria tarefas de transformação específicas da unidade de negócio"""
        pass
    
    @abstractmethod
    def _create_load_tasks(self) -> List[PythonOperator]:
        """Cria tarefas de carregamento específicas da unidade de negócio"""
        pass
    
    @abstractmethod
    def _set_task_dependencies(self) -> None:
        """Define dependências entre tarefas"""
        pass
    
    @abstractmethod
    def _get_report_function(self):
        """Retorna função de relatório específica"""
        pass
    
    def get_dag(self) -> DAG:
        """Retorna a instância da DAG"""
        return self.dag
    
    def is_csv_mode(self) -> bool:
        """Verifica se está no modo CSV"""
        return self.output_mode.lower() == 'csv'
    
    def is_salesforce_mode(self) -> bool:
        """Verifica se está no modo Salesforce"""
        return self.output_mode.lower() == 'salesforce'
