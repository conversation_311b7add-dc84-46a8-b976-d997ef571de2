#!/usr/bin/env python3
"""
Teste de Correção de Imports

Testa se a função get_extracted_data_from_xcom pode ser importada
corretamente pelas transformações específicas por unidade de negócio.
"""

import sys
import os
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_get_extracted_data_from_xcom_import():
    """Testa se a função get_extracted_data_from_xcom pode ser importada"""
    logger.info("🧪 Testando importação de get_extracted_data_from_xcom...")
    
    try:
        # Simular o contexto das transformações
        sys.path.insert(0, '.')
        
        # Tentar importar a função como fazem as transformações
        from etl_main import get_extracted_data_from_xcom
        
        logger.info("✅ get_extracted_data_from_xcom importada com sucesso")
        
        # Testar se a função é chamável
        if callable(get_extracted_data_from_xcom):
            logger.info("✅ get_extracted_data_from_xcom é uma função válida")
        else:
            logger.error("❌ get_extracted_data_from_xcom não é uma função válida")
            return False
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ Erro de importação: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Erro inesperado: {e}")
        return False

def test_business_unit_transformers_functions():
    """Testa se as funções de transformação podem ser importadas"""
    logger.info("🧪 Testando importação das funções de transformação...")
    
    try:
        # Importar o módulo
        import business_unit_transformers
        
        # Lista de funções que devem existir
        expected_functions = [
            'airflow_transform_clientes_consorcio_task',
            'airflow_transform_leads_consorcio_task',
            'airflow_transform_produtos_consorcio_task',
            'airflow_transform_propostas_consorcio_task',
            'airflow_transform_clientes_seguros_task',
            'airflow_transform_leads_seguros_task',
            'airflow_transform_produtos_seguros_task',
            'airflow_transform_propostas_seguros_task',
        ]
        
        missing_functions = []
        for func_name in expected_functions:
            if not hasattr(business_unit_transformers, func_name):
                missing_functions.append(func_name)
            else:
                func = getattr(business_unit_transformers, func_name)
                if not callable(func):
                    missing_functions.append(f"{func_name} (não é função)")
        
        if missing_functions:
            logger.error(f"❌ Funções faltando: {missing_functions}")
            return False
        else:
            logger.info(f"✅ Todas as {len(expected_functions)} funções de transformação encontradas")
            return True
        
    except Exception as e:
        logger.error(f"❌ Erro ao testar funções: {e}")
        return False

def test_mock_transformation_call():
    """Testa uma chamada simulada de transformação"""
    logger.info("🧪 Testando chamada simulada de transformação...")
    
    try:
        import business_unit_transformers
        
        # Simular contexto do Airflow (sem task instance real)
        mock_context = {
            'ti': None,  # Sem task instance real
            'dag': None,
            'task': None
        }
        
        # Tentar chamar uma função de transformação (deve falhar graciosamente)
        try:
            result = business_unit_transformers.airflow_transform_produtos_seguros_task(**mock_context)
            logger.warning("⚠️ Transformação não deveria ter funcionado sem task instance")
            return False
        except Exception as e:
            # Esperamos que falhe devido à falta de task instance
            if "etl_main" in str(e) or "get_extracted_data_from_xcom" in str(e):
                logger.info("✅ Transformação falhou como esperado (sem task instance)")
                return True
            else:
                logger.error(f"❌ Transformação falhou por motivo inesperado: {e}")
                return False
        
    except Exception as e:
        logger.error(f"❌ Erro no teste de chamada simulada: {e}")
        return False

def run_import_tests():
    """Executa todos os testes de importação"""
    logger.info("🚀 Iniciando testes de correção de imports...")
    
    tests = [
        ("Importação get_extracted_data_from_xcom", test_get_extracted_data_from_xcom_import),
        ("Funções de Transformação", test_business_unit_transformers_functions),
        ("Chamada Simulada", test_mock_transformation_call),
    ]
    
    results = {}
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"🧪 Executando: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name}: PASSOU")
            else:
                logger.error(f"❌ {test_name}: FALHOU")
                
        except Exception as e:
            logger.error(f"❌ {test_name}: ERRO - {e}")
            results[test_name] = False
    
    # Relatório final
    logger.info(f"\n{'='*50}")
    logger.info("📊 RELATÓRIO FINAL - CORREÇÃO DE IMPORTS")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSOU" if result else "❌ FALHOU"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nResultado: {passed}/{total} testes passaram")
    
    if passed == total:
        logger.info("🎉 Todos os testes de import passaram!")
        logger.info("✅ As transformações devem funcionar no Airflow")
        return True
    else:
        logger.error("💥 Alguns testes falharam. Verifique os logs acima.")
        return False

if __name__ == "__main__":
    success = run_import_tests()
    sys.exit(0 if success else 1)
